
# Tech Context

## Key Technologies

- React (v18+): UI library for building feature-based SPAs
- Vite: Fast build tool and dev server
- TypeScript (strict mode): Static typing for reliability
- Zustand: Global API state management
- React Hook Form: Form state and validation
- axiosInstance: API calls with JWT and interceptors
- MUI v5+ and Emotion: UI components and styling
- ESLint: Strict linting
- Prettier: Code formatting
- Vitest, React Testing Library: Unit and integration testing

## Development Environment

- Node.js / npm
- VS Code (recommended)
- Git for version control

## API Dependencies

- Identity Server (OAuth2/JWT): https://a002micdev01.cloud.mdrxdev.com:6768/api
- Application backend API: Proxied via `/api` in development

## Style Guide

- ESLint and Prettier enforced
- Strict TypeScript configuration
- Feature-based folder structure
- No console.log; use custom loggers

## Core Technologies

- **Frontend:** React (v18+), TypeScript, Vite
- **UI:** MUI v5+, Emotion
- **State Management:** Zustand (global API data), React Hook Form (forms)
- **API:** axiosInstance with JWT, error interceptors
- **Testing:** Vitest, React Testing Library, renderWithProviders

## Coding Standards & Conventions

- ESLint with recommended configs
- Prettier formatting
- Strict TypeScript (`tsconfig.app.json`)
- Container/presenter pattern for feature modules

## Testing Frameworks

- Vitest for unit/integration tests
- React Testing Library for component tests
- All tests use `renderWithProviders` and mock stores

## Build & Deployment

- Build: `npm run build` (TypeScript → Vite)
- Dev: `npm run dev` (Vite dev server)
- Preview: `npm run preview` (local prod test)
- Deploy: `./deploy-to-iis.ps1` (Windows IIS)
- Environment: `.env.development`, `.env.production`

## Notes

- Modern React/TypeScript stack with strict code quality
- Feature-based architecture for scalability
- All API calls use JWT and error handling
- Testing and deployment scripts are in place