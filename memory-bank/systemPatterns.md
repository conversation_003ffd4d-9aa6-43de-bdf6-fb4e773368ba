
# System Patterns

## Architecture Overview

- Frontend: React (Vite)
- State Management: Zustand for global API data, React Hook Form for forms
- API Communication: axiosInstance with JWT and interceptors
- Authentication: OAuth2/JWT with automatic refresh

## Key Technical Decisions

- Use TypeScript strict mode for type safety
- Use Zustand for scalable, modular state management
- Use React Hook Form with MUI Controller for all forms
- Use axiosInstance for API calls, with JWT and error handling
- Use ErrorBoundary for graceful error handling
- Use custom loggers (`chartLogger`, `storeLogger`, `apiLogger`) for all logging

## Data Flow (Authentication)

1. User initiates login
2. JWT is obtained via OAuth2 flow
3. Token is stored securely and used for API requests
4. axiosInstance adds token to headers and handles 401 errors
5. Automatic token refresh and logout coordination via event manager

## Architecture Details

- **Type:** Frontend SPA
- **Framework:** React (v18+) with TypeScript
- **Build System:** Vite
- **UI:** MUI v5+ with Emotion
- **State:** <PERSON><PERSON>and (global API data), React Hook Form (forms)
- **Grid:** MUI DataGrid Premium with layout persistence
- **Authentication:** OAuth2/JWT, automatic refresh, secure storage
- **Error Handling:** ErrorBoundary, custom loggers
- **Testing:** Vitest, React Testing Library, renderWithProviders, mock stores

## Core Components

- `src/App.tsx`: Main app component
- `src/config/routes.ts`: Centralized route config
- `src/features/{apl,auth,chart}/`: Feature modules
- `src/shared/components/ErrorBoundary.tsx`: Error handling
- `src/shared/components/LoadingPage.tsx`: Loading states
- `src/shared/components/NotFoundPage.tsx`: 404 page
- `src/utils/logger.ts`: Logging utilities

## Styling

- Emotion and MUI theme in `src/theme/index.ts`
- CssBaseline for base styling
- Strict markdown formatting for docs

## Markdown Formatting Standards

- MD036: No emphasis as heading
- MD022: Blank line before/after every heading
- MD032: Blank line before/after every list
- MD058: Blank line before/after every table
- MD047: One trailing newline
- MD007: 2 spaces for list indentation
- MD009: No trailing spaces
- MD031: Blank line before/after code blocks
- MD040: All code blocks specify language

## Potential Future Enhancements

- Backend integration for persistent layout storage
- Expansion of feature modules
- Additional custom hooks for complex form validation
- More advanced error boundary and logging features
- **Base Styling:** `CssBaseline` from MUI for cross-browser consistency.
