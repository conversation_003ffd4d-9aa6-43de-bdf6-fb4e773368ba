
# Project Brief

## Project Name

vic-react

## Project Goals

- Implement secure user authentication using OAuth2/JWT
- Develop a feature-based React app with modular architecture
- Use Zustand for global API state and React Hook Form for forms
- Integrate MUI DataGrid Premium with layout persistence
- Achieve strict TypeScript and ESLint compliance
- Ensure robust error handling and logging
- Provide comprehensive test coverage

## Target Users

Healthcare providers, clinical staff, and facility administrators who need secure, efficient access to patient and facility data.

## Success Metrics

- Successful login/logout and session management
- Secure handling of JWT tokens and automatic refresh
- Efficient API data fetching and state management with Zustand
- Responsive, modern UI with MUI components
- Zero TypeScript and ESLint errors
- High test coverage and reliability

## Project Mission

Deliver a modern, secure, and highly usable healthcare data management platform that streamlines charting, patient list management, and facility operations.

## Problem Statement

Legacy healthcare applications are slow, hard to maintain, and lack modern UX and security. This project solves these issues by providing a scalable, maintainable, and user-friendly React app with advanced grid and authentication features.

## Key Objectives

- Replace legacy VB.NET app with a modern React solution
- Ensure HIPAA-compliant authentication and data access
- Provide advanced grid features for layout saving/restoring
- Maintain strict code quality and testing standards

## Scope

### In Scope

- OAuth2/JWT authentication
- Feature-based architecture (APL, Auth, Chart)
- Zustand store for API data
- React Hook Form for forms
- MUI DataGrid Premium with layout persistence
- Error boundaries and custom logging
- Comprehensive testing

### Out of Scope

- Legacy desktop app maintenance
- Backend development (API assumed to exist)
- Mobile app development

## Stakeholders

- Clinical Staff: End users of charting and patient list features
- Facility Administrators: Oversee data management and compliance
- IT/Dev Team: Maintain and extend the React app