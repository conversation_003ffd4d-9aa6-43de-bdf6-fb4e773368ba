# Task Management

This document is the **SINGLE SOURCE OF TRUTH** for all task tracking.

## Task Status Legend
- **PENDING:** Task is planned but not yet started.
- **IN PROGRESS:** Task is actively being worked on.
- **BLOCKED:** Task is blocked by an external factor.
- **REVIEW:** Task is completed and awaits review.
- **DONE:** Task is completed, reviewed, and verified.
- **ARCHIVED:** Task is done and moved to `docs/archive/completed_tasks.md`.

## Active Tasks

---

## Task: Investigate Application and Update systemPatterns.md

**ID:** 1
**Status:** IN PROGRESS
**Complexity:** Level 3
**Assigned To:** AI
**Description:** Conduct a thorough investigation of the application codebase and update `memory-bank/systemPatterns.md` with detailed information about the system architecture, key technical decisions, data flow, and core components.
**ETA:** TBD
**Dependencies:** None
**History:**
- [2024-07-14 10:00:00] - Task created.
**Checklist:**
- [x] INITIALIZATION: Confirm OS, `.cursorrules`, `memory-bank/`, `docs/archive/`.
- [x] INITIALIZATION: Create missing Memory Bank files.
- [x] INITIALIZATION: Create `tasks.md` and add this task.
- [x] DOCUMENTATION: Update `tasks.md` status to IN PROGRESS.
- [x] DOCUMENTATION: Update `activeContext.md` with initial investigation plan.
- [ ] PLANNING: Refine investigation plan based on initial findings.
- [ ] PLANNING: Document plan in `progress.md`.
- [x] IMPLEMENTATION: Read `package.json`.
- [x] IMPLEMENTATION: Read `vite.config.ts`.
- [x] IMPLEMENTATION: Read `tsconfig.json`, `tsconfig.app.json`.
- [x] IMPLEMENTATION: Read `eslint.config.js`.
- [x] IMPLEMENTATION: Explore `src/` directory and key files (`main.tsx`, `App.tsx`).
- [x] IMPLEMENTATION: Identify core components, pages, theme, types, utils.
- [x] IMPLEMENTATION: Update `systemPatterns.md` with findings.
- [x] IMPLEMENTATION: Update `techContext.md` with findings.
- [x] DOCUMENTATION: Update `activeContext.md` with progress and findings.
- [ ] DOCUMENTATION: Update `progress.md` with implementation details.
- [ ] REFLECTION: Review changes to `systemPatterns.md` and `techContext.md`.
- [ ] REFLECTION: Verify against Level 3 checklist.
- [ ] REFLECTION: Update `progress.md` with reflection summary.
- [ ] DOCUMENTATION: Update `tasks.md` status to DONE.
- [ ] ARCHIVING: Archive relevant details from `activeContext.md` to `docs/archive/completed_tasks.md`.
- [ ] ARCHIVING: Clear `activeContext.md` for the next task.

---

## Future/Backlog Tasks

# Tasks

## Task ID Format: `TASK-XXX` (e.g., `TASK-001`)

---

### `TASK-001`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** System
- **Assignee:** Vic
- **Description:** Initial project setup and creation of Memory Bank files.
- **Acceptance Criteria:**
  - `.cursorrules` file created.
  - `memory-bank/` directory created with all core files.
  - `docs/archive/completed_tasks.md` created.
- **History:**
  - `2025-05-16 00:00:00Z`: Task created.
  - `2025-05-16 00:00:00Z`: Created .cursorrules.
  - `2025-05-16 00:00:00Z`: Created memory-bank directory and core files.
  - `2025-05-16 00:00:00Z`: Task completed.

---

### `TASK-002`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** Vic
- **Description:** Rename `MainScreen` component and file to `AplPage`.
- **Acceptance Criteria:**
  - `MainScreen.tsx` is renamed to `AplPage.tsx`.
  - The component `MainScreen` within the file is renamed to `AplPage`.
  - All imports of `MainScreen` are updated to `AplPage`.
- **Linked Files:** `[src/pages/AplPage.tsx, src/App.tsx]`
- **History:**
  - `2025-05-16 00:00:00Z`: Task created.
  - `2025-05-16 00:00:00Z`: Renaming process started.
  - `2025-05-16 00:00:00Z`: Renamed `MainScreen.tsx` to `AplPage.tsx`, updated component and exports. Updated imports in `App.tsx`.
  - `2025-05-16 00:00:00Z`: Task marked as DONE.

---

### `TASK-003`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** Vic
- **Description:** Wrap `AplPage` with `SharedLayout` in `App.tsx`.
- **Acceptance Criteria:**
  - `AplPage` component in `App.tsx` is wrapped by the `SharedLayout` component.
  - The application renders correctly with `SharedLayout`.
- **Linked Files:** `[src/App.tsx, src/components/layout/SharedLayout.tsx]`
- **History:**
  - `2025-05-16 00:00:00Z`: Task created.
  - `2025-05-16 00:00:00Z`: Started wrapping AplPage with SharedLayout.
  - `2025-05-16 00:00:00Z`: Wrapped `AplPage` with `SharedLayout` in `App.tsx` and added import.
  - `2025-05-16 00:00:00Z`: Task marked as DONE.

---

### `TASK-004` 
- **Status:** `Implementation Complete - Pending Testing`
- **Complexity:** Level 3
- **Requester:** User (derived from previous interactions)
- **Assignee:** Vic
- **Description:** Add ability to save and restore grid layouts by name.
- **Requirements:** User can save current grid configuration (column order, size, sort order, grouping, filtering) with a name. User can see a list of saved layouts and restore one. Save/Load buttons should be on the same line as the grid toolbar.
- **Acceptance Criteria:**
    1. User can click a 'Save Layout' button.
    2. User is prompted to enter a name for the layout.
    3. Upon saving, the layout (including column order, visibility, size, sort, active filters, active grouping) is stored (e.g., in localStorage).
    4. User can click a 'Load Layout' button (or a dropdown showing saved layouts).
    5. Selecting a saved layout restores the grid to the saved state.
    6. UI elements are visually integrated with the existing grid toolbar.
- **Dependencies:** `[TASK-001]`
- **Linked Files:** `[src/components/table/CustomGridToolbar.tsx, src/components/table/DataRequesterGrid.tsx]`
- **History:**
    - `2024-07-25 12:00:00Z`: Task status changed to In Progress.
    - `2024-07-25 12:05:00Z`: Planning complete. Identified MUI X DataGrid state functions and localStorage for persistence. Custom toolbar planned.
    - `2024-07-25 12:15:00Z`: Created `src/components/table/CustomGridToolbar.tsx` with save/load/delete logic.
    - `2024-07-25 12:20:00Z`: Modified `src/components/table/DataRequesterGrid.tsx` to use `apiRef` and `CustomGridToolbar`.
    - `2024-07-25 12:20:00Z`: Implementation of core save/restore functionality complete. Moving to testing phase.
    - `2025-05-16 00:00:00Z`: Task status changed from Not Started.

---

### `TASK-999`
- **Status:** `TESTING`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** This is a test task to demonstrate the new timestamp format.
- **Acceptance Criteria:**
  - Timestamps in history use YYYY-MM-DD HH:MM:SS format.
  - No 'VIC' suffix is present.
- **History:**
  - `2025-05-16 13:34:32Z`: Test task created by AI.
  - `2025-05-16 13:34:32Z`: Timestamp format demonstrated.

---

### `TASK-005`
- **Status:** `DONE`
- **Complexity:** Level 2
- **Requester:** User
- **Assignee:** Vic
- **Description:** Configure VS Code launch.json for launching the Vite app, opening with Chrome, and enabling breakpoints.
- **Acceptance Criteria:**
  - `launch.json` file is created or updated in the `.vscode` directory.
  - Configuration includes an option to run the Vite dev server (e.g., `npm run dev`).
  - Configuration includes an option to launch Chrome and attach the debugger to `http://localhost:5173`.
  - Breakpoints set in VS Code for frontend code (TSX/JSX) are hit when debugging with Chrome.
- **Linked Files:** `[.vscode/launch.json, vite.config.ts, package.json]`
- **History:**
  - `2025-05-17 00:00:00Z`: Task created.
  - `2025-05-17 00:00:01Z`: Started configuring launch.json.
  - `2025-05-17 00:00:02Z`: Created .vscode/launch.json with configurations for Vite dev server and Chrome debugging.
  - `2025-05-17 00:00:03Z`: User confirmed launch configuration works as expected. Task completed.

# Tasks - User Login Implementation (OAuth2)

## Task ID: AUTH-001
- **Description**: Setup OAuth2 Configuration and Environment Variables
- **Status**: DONE
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 2
- **Estimated Time**: 2 hours
- **Sub-Tasks**:
    - `AUTH-001.1`: Identify and document OAuth2 provider endpoints (authorize, token, userinfo). - DONE
    - `AUTH-001.2`: Obtain `client_id` and `client_secret` (if applicable for the chosen flow) from the Identity Provider. - DONE (Client ID obtained, Client Secret noted but PKCE preferred)
    - `AUTH-001.3`: Configure redirect URIs in the Identity Provider. - User Action Required (manual step in IDP)
    - `AUTH-001.4`: Add OAuth2 configuration variables (client ID, authority/issuer URL, redirect URI, scopes) to `.env` and `.env.example` files. - DONE (User created/updated files)
    - `AUTH-001.5`: Create a configuration file/module in `src/config/` or `src/utils/` to export these variables for application use. - DONE
- **Acceptance Criteria**:
    - All necessary OAuth2 configuration parameters are securely stored and accessible within the application. - DONE
    - `.env.example` reflects all required environment variables for authentication. - DONE (User created file)
- **Updates**:
    - [Timestamp] Task status changed to DONE.
    - [Timestamp] User manually created/updated `.env` and `.env.example`.
    - [Timestamp] Created `src/config/authConfig.ts`.

## Task ID: AUTH-002
- **Description**: Install and Configure Authentication Libraries
- **Status**: On Hold
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 2
- **Estimated Time**: 1 hour
- **Sub-Tasks**:
    - `AUTH-002.1`: Research and choose a suitable OAuth2/OIDC client library for React (e.g., `oidc-client-ts`, `react-oauth2-code-pkce`).
    - `AUTH-002.2`: Install the chosen library and its dependencies (`npm install <library-name>`).
    - `AUTH-002.3`: Initialize and configure the library with the parameters from `AUTH-001`.
- **Acceptance Criteria**:
    - Selected OAuth2 client library is installed and configured correctly.
    - The library can be instantiated with the project's OAuth2 settings.
- **Updates**:

## Task ID: AUTH-003
- **Description**: Implement Authentication Context/Provider
- **Status**: On Hold
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 3
- **Estimated Time**: 4 hours
- **Sub-Tasks**:
    - `AUTH-003.1`: Create a React Context (`AuthContext`) to manage authentication state (user, tokens, loading status, errors).
    - `AUTH-003.2`: Implement `AuthProvider` component to wrap the application and provide `AuthContext`.
    - `AUTH-003.3`: Implement login function:
        - Initiates the OAuth2 authorization flow (redirect to IDP).
    - `AUTH-003.4`: Implement token handling function:
        - Called on redirect from IDP.
        - Exchanges authorization code for tokens (if using Authorization Code Flow).
        - Stores tokens securely (e.g., in memory, potentially using the chosen library's storage mechanism).
        - Fetches user information using the access token.
        - Updates `AuthContext` with user and token data.
    - `AUTH-003.5`: Implement logout function:
        - Clears tokens and user data from `AuthContext` and storage.
        - Optionally, redirects to IDP for session termination.
    - `AUTH-003.6`: Implement silent refresh mechanism for access tokens using the refresh token.
- **Acceptance Criteria**:
    - `AuthContext` provides user, token, and authentication status.
    - Login function successfully redirects to IDP and handles callback.
    - Logout function clears session and redirects if necessary.
    - Tokens are stored and managed securely.
    - Silent token refresh works as expected.
- **Updates**:

## Task ID: AUTH-004
- **Description**: Create Login UI Components
- **Status**: On Hold
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 2
- **Estimated Time**: 3 hours
- **Sub-Tasks**:
    - `AUTH-004.1`: Create a `LoginPage` component (`src/pages/LoginPage.tsx`).
    - `AUTH-004.2`: Design and implement a login form/button that triggers the login function from `AuthContext`.
    - `AUTH-004.3`: Create a `CallbackPage` component (`src/pages/CallbackPage.tsx`) to handle the redirect from the IDP and call the token handling function. This page should display a loading indicator.
    - `AUTH-004.4`: Create a `LogoutButton` component that calls the logout function from `AuthContext`.
    - `AUTH-004.5`: Display user information or a welcome message when logged in.
- **Acceptance Criteria**:
    - `LoginPage` allows users to initiate the login process.
    - `CallbackPage` successfully processes the IDP redirect and token exchange.
    - `LogoutButton` successfully logs the user out.
    - UI elements reflect the current authentication state.
- **Updates**:

## Task ID: AUTH-005
- **Description**: Implement Protected Routes
- **Status**: On Hold
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 2
- **Estimated Time**: 2 hours
- **Sub-Tasks**:
    - `AUTH-005.1`: Create a `ProtectedRoute` higher-order component (HOC) or wrapper component.
    - `AUTH-005.2`: This component checks authentication status from `AuthContext`.
    - `AUTH-005.3`: If not authenticated, redirects to `LoginPage`.
    - `AUTH-005.4`: If authenticated, renders the wrapped component.
    - `AUTH-005.5`: Update application routing to use `ProtectedRoute` for relevant routes.
- **Acceptance Criteria**:
    - Unauthenticated users are redirected to the login page when trying to access protected routes.
    - Authenticated users can access protected routes.
- **Updates**:

## Task ID: AUTH-TEMP-001
- **Description**: Implement Direct Token Request Service Call (Temporary Dev Solution)
- **Status**: DONE
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 2
- **Estimated Time**: 3 hours
- **Sub-Tasks**:
    - `AUTH-TEMP-001.1`: Create a simple React login form component (`src/components/auth/LoginForm.tsx`). - DONE
    - `AUTH-TEMP-001.2`: On form submission, use Axios to make a POST request to the token endpoint (`authConfig.tokenEndpoint`). - DONE (within AuthContext)
    - `AUTH-TEMP-001.3`: Ensure the request body is `x-www-form-urlencoded` and includes required parameters. - DONE (within AuthContext)
    - `AUTH-TEMP-001.4`: Store the received `access_token` (and `user_id` if available) in a temporary client-side store (React Context). - DONE
    - `AUTH-TEMP-001.5`: Display basic user/token information or an error message. - DONE (within LoginForm)
- **Acceptance Criteria**:
    - User can enter credentials into a login form. - DONE
    - On submission, an access token is successfully retrieved from the token endpoint. - DONE
    - The access token (and user identifier, if possible) is stored client-side. - DONE
    - Errors during login are displayed to the user. - DONE
- **Security Warning**: This Resource Owner Password Credentials Grant flow directly from the SPA is **NOT FOR PRODUCTION**. It is a temporary measure for development to obtain a user context.
- **Updates**:
    - [Timestamp] Created `src/components/auth/LoginForm.tsx`.
    - [Timestamp] Installed axios.
    - [Timestamp] Created `src/contexts/AuthContext.tsx` for token management.
    - [Timestamp] Refactored `LoginForm.tsx` to use `AuthContext`.
    - [Timestamp] Wrapped App with `AuthProvider` in `main.tsx`.
    - [Timestamp] Created `src/pages/LoginPage.tsx` and updated `App.tsx` to enforce login.
    - [Timestamp] Resolved CORS issue by configuring server.
    - [Timestamp] Resolved `invalid_scope` issue by simplifying scopes to `api1 offline_access`.
    - [Timestamp] Login successful, token received.

## Task ID: UI-AUTH-001
- **Description**: Display Logged-In User Name in AppHeader
- **Status**: DONE
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 2
- **Estimated Time**: 1.5 hours
- **Sub-Tasks**:
    - `UI-AUTH-001.1`: Install a lightweight JWT decoding library (e.g., `jwt-decode`). - DONE
    - `UI-AUTH-001.2`: In `AuthContext.tsx`, when a token is received: - DONE
        - Decode the `access_token`. - DONE
        - Extract the name claim (`http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name`). - DONE
        - Store the user object (e.g., `{ username: originalUsername, displayName: extractedName }`) in the `authState`. - DONE
    - `UI-AUTH-001.3`: Update the `User` interface in `AuthContext.tsx` if needed. - DONE
    - `UI-AUTH-001.4`: Modify `src/components/layout/AppHeader.tsx` to use `useAuth()` and display `user.displayName` if available. - DONE
- **Acceptance Criteria**:
    - The user's name (e.g., "Enchart Admin") is correctly displayed in the `AppHeader` after a successful login. - DONE
    - If the name claim is not present, a default (like the username) or nothing is shown gracefully. - DONE
- **Updates**:
    - [Timestamp] Installed `jwt-decode`.
    - [Timestamp] Updated `AuthContext.tsx` to decode JWT and store user display name.
    - [Timestamp] Updated `AppHeader.tsx` to display the user's name from context.
    - [Timestamp] User confirmed functionality is working.

## Task ID: AUTH-006
- **Description**: Setup Axios and React Query for API Calls
- **Status**: DONE
- **Assignee**: AI
- **Priority**: Medium
- **Complexity**: 3
- **Estimated Time**: 3 hours
- **Sub-Tasks**:
    - `AUTH-006.1`: Install Axios and React Query (`npm install @tanstack/react-query`). - DONE (Axios was already present)
    - `AUTH-006.2`: Create an Axios instance (`src/api/axiosInstance.ts`). - DONE
    - `AUTH-006.3`: Configure Axios interceptors to automatically add the access token to outgoing requests. - DONE
    - `AUTH-006.4`: Implement logic in the interceptor to handle 401 errors (e.g., by attempting a token refresh or logging the user out). - DONE (temporary logout)
    - `AUTH-006.5`: Initialize `QueryClient` and wrap the application with `QueryClientProvider` (`src/main.tsx`). - DONE
- **Acceptance Criteria**:
    - Axios instance is configured with an interceptor to include the access token in API requests. - DONE
    - React Query is set up and available throughout the application. - DONE
    - API calls automatically include the token. - Implemented, pending test in AUTH-007
    - Token refresh or logout is handled on 401 responses. - DONE (logout on 401)
- **Updates**:
    - [Timestamp] Installed `@tanstack/react-query`.
    - [Timestamp] Created `src/api/axiosInstance.ts` with base config.
    - [Timestamp] Created `src/api/tokenManager.ts`.
    - [Timestamp] Updated `AuthContext.tsx` to use `tokenManager`.
    - [Timestamp] Added request (auth header) and response (401 handler) interceptors to `axiosInstance`.
    - [Timestamp] Wrapped app with `QueryClientProvider` in `main.tsx`.

## Task ID: AUTH-007
- **Description**: Integrate API Calls with Authentication
- **Status**: To Do
- **Assignee**: AI
- **Priority**: Medium
- **Complexity**: 2
- **Estimated Time**: 2 hours
- **Sub-Tasks**:
    - `AUTH-007.1`: Create example API calls using the configured Axios instance and React Query's `useQuery` or `useMutation` hooks.
    - `AUTH-007.2`: Ensure these API calls work correctly with the authentication system (i.e., requests are authorized).
    - `AUTH-007.3`: Test fetching data from a protected API endpoint.
- **Acceptance Criteria**:
    - React Query hooks can successfully fetch data from protected API endpoints using the access token.
    - API calls are correctly authorized.
- **Updates**:

## Task ID: AUTH-008
- **Description**: Testing and Refinement
- **Status**: To Do
- **Assignee**: AI
- **Priority**: High
- **Complexity**: 3
- **Estimated Time**: 4 hours
- **Sub-Tasks**:
    - `AUTH-008.1`: Thoroughly test the entire login, logout, and token refresh flow.
    - `AUTH-008.2`: Test protected routes and API access.
    - `AUTH-008.3`: Test error handling (e.g., incorrect credentials, IDP errors, network issues).
    - `AUTH-008.4`: Perform cross-browser testing (if applicable).
    - `AUTH-008.5`: Refactor and clean up code.
    - `AUTH-008.6`: Update documentation in Memory Bank files.
- **Acceptance Criteria**:
    - Authentication system is robust and handles various scenarios correctly.
    - Code is clean, well-documented, and adheres to project standards.
- **Updates**:

## Task ID: **Task:** Refactor `InboundProcessors` and update `InboundTranslator2.vb`.
- **Status:** DONE
- **Complexity:** 3
- **Details:**
  - Target directory: `Server/EnchartServer/src/EnchartServer.Business/CAC/InboundProcessors/`
  - Refactored files (prefixed "IT2", originals deleted):
    - `IT2AssessmentsProcessor.vb`
    - `IT2ClinicProcessor.vb`
    - `IT2ClinicProfessionalProcessor.vb`
    - `IT2DispositionProcessor.vb`
    - `IT2EMGridProcessor.vb`
    - `IT2FacilityModifiersProcessor.vb`
    - `IT2ICD9Processor.vb`
    - `IT2InfusionProcessor.vb`
    - `IT2SurgicalLacsProcessor.vb`
    - `IT2MiscellaneousProcessor.vb`
    - `IT2ObservationModifiersProcessor.vb`
    - `IT2ObservationAllocateProceduresProcessor.vb`
    - `IT2ObservationAllocateRepUnitsProcessor.vb`
    - `IT2ObservationMedsProcessor.vb`
    - `IT2ObservationProcsProcessor.vb`
    - `IT2ObservationTimesProcessor.vb`
    - `IT2PhysicianProcessor.vb`
    - `IT2SuppliesProcessor.vb`
    - `IT2TimesProcessor.vb`
    - `IT2QualityIndicatorsProcessor.vb`
  - Remaining original files to process:
    - `SurgicalMiscProcessor.vb`
    - `SurgicalFBIDProcessor.vb`
    - `OrthopedicsProcessor.vb`
    - `Procedures02Processor.vb`
    - `Procedures01Processor.vb`
    - `TestsProcessor.vb`
    - `PsychosocialProcessor.vb`
    - `TriageProcessor.vb`
  - Current state: All individual InboundProcessor files have been refactored.
  - Next immediate step: Update `Server/EnchartServer/src/EnchartServer.Business/CAC/InboundTranslator2.vb` to reflect all class name changes.
  - Final overall goal: After all `.vb` files in the target directory are refactored, update `Server/EnchartServer/src/EnchartServer.Business/CAC/InboundTranslator2.vb` to reflect all class name changes.
  - **Subtasks:**
    - [x] Create `IT2SurgicalMiscProcessor.vb` and delete original.
    - [x] Create `IT2SurgicalFBIDProcessor.vb` and delete original.
    - [x] Create `IT2OrthopedicsProcessor.vb` and delete original.
    - [x] Create `IT2Procedures02Processor.vb` and delete original.
    - [x] Create `IT2Procedures01Processor.vb` and delete original.
    - [x] Create `IT2TestsProcessor.vb` and delete original.
    - [x] Create `IT2PsychosocialProcessor.vb` and delete original.
    - [x] Create `IT2TriageProcessor.vb` and delete original.
    - [x] Update `InboundTranslator2.vb` with new class names.

## Section: UI Enhancements - ChartPage

### `UI-CHARTPAGE-001`
- **Status:** `DONE`
- **Complexity:** Level 2
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Add a fixed toolbar with input fields to the top of the `ChartPage`.
- **Acceptance Criteria:**
  - A toolbar is visible at the top of `ChartPage` and remains fixed during scrolling.
  - Toolbar contains input fields for: Visit ID, Medical Record Number, Last, First, DOB, Age.
  - Toolbar contains input/display fields for: ED DOS, ED Start, ED End, OBS Start, OBS End.
  - Toolbar contains checkboxes for: ED, OBS.
  - Main content of `ChartPage` is not obscured by the toolbar and is scrollable.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created.
  - `2024-07-27`: Implemented fixed AppBar with Toolbar, TextField inputs, and Checkboxes in `ChartPage.tsx`.
  - `2024-07-27`: Added state management for toolbar inputs.
  - `2024-07-27`: Ensured main content is scrollable below the fixed toolbar.
  - `2024-07-27`: Task marked as DONE.

### `UI-CHARTPAGE-002`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Refine `ChartPage` toolbar layout to use two rows for controls, as per image.
- **Acceptance Criteria:**
  - Toolbar controls in `ChartPage.tsx` are arranged in two distinct rows.
  - Row 1: Visit ID, Medical Record Number, Last, First, DOB, Age.
  - Row 2: ED DOS, ED Start, ED End, OBS Start, OBS End, ED Checkbox, OBS Checkbox.
  - "ED Start" is a separate `TextField`.
  - Main content is not obscured by the taller toolbar.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created for layout refinement.
  - `2024-07-27`: Modified `Toolbar` and added nested `Box` components for two-row layout. Re-added "ED Start" field. Adjusted main content padding.
  - `2024-07-27`: Task marked as DONE.

### `UI-CHARTPAGE-003`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Adjust horizontal spacing of `ChartPage` toolbar controls using `justifyContent: 'space-between'`.
- **Acceptance Criteria:**
  - Controls within each row of the toolbar are horizontally spaced out.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created for horizontal spacing.
  - `2024-07-27`: Added `justifyContent: 'space-between'` to row Box components in `ChartPage.tsx`.
  - `2024-07-27`: Task marked as DONE.

### `UI-CHARTPAGE-004`
- **Status:** `DONE`
- **Complexity:** Level 2
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Restructure `ChartPage` toolbar to a six-column grid layout for vertical alignment of fields.
- **Acceptance Criteria:**
  - Toolbar uses MUI `Grid` for layout.
  - Controls are arranged in two rows, with items in the second row aligning vertically with items in the first.
  - Each conceptual column (6 total) is represented by `Grid item xs={2}` or similar.
  - Row 1: Visit ID, MRN, Last, First, DOB, Age.
  - Row 2: ED DOS, ED Start, ED End, OBS Start, OBS End, [ED Checkbox + OBS Checkbox group].
  - Main content is not obscured by the toolbar.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created for six-column grid layout.
  - `2024-07-27`: Implemented MUI Grid layout in `ChartPage.tsx`. Used `Grid container` for rows and `Grid item xs={12} sm={6} md={2}` for fields. Styled TextFields for full width within grid cells. Grouped checkboxes. Adjusted main content padding.

### `UI-CHARTPAGE-005`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Update checkbox labels in `ChartPage` toolbar to "ED Required" and "OBS Required".
- **Acceptance Criteria:**
  - First checkbox label is "ED Required".
  - Second checkbox label is "OBS Required".
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created for label update.
  - `2024-07-27`: Changed `label` prop on `FormControlLabel` components for ED and OBS checkboxes in `ChartPage.tsx`.

### `UI-CHARTPAGE-006`
- **Status:** `FAILED`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Improve checkbox label visibility in `ChartPage` toolbar by changing text color.
- **Acceptance Criteria:**
  - Checkbox labels are clearly visible against the AppBar background.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to improve label visibility.
  - `2024-07-27`: Attempted to change `color: 'common.white'` to `color: 'text.primary'` for `FormControlLabel` sx prop.
  - `2024-07-27`: `edit_file` tool repeatedly failed to apply the change correctly, or misreported file state, leading to file corruption (incorrect component placement, ReferenceErrors).
  - `2024-07-27`: Task marked FAILED due to tool issues. Corrective action taken in `UI-CHARTPAGE-007`.

### `UI-CHARTPAGE-007`
- **Status:** `DONE`
- **Complexity:** Level 2
- **Requester:** User (derived from error state)
- **Assignee:** AI Assistant
- **Description:** Correct `ChartPage.tsx` layout, state variable definitions, and checkbox errors caused by previous faulty edits.
- **Acceptance Criteria:**
  - `ChartPage.tsx` renders without `ReferenceError` issues.
  - Toolbar uses a six-column `Grid` layout with two rows, and fields are vertically aligned.
  - All state variables for toolbar controls are correctly defined and scoped.
  - Checkbox labels are "ED Required" and "OBS Required".
  - Checkbox label color is `text.primary`, providing good visibility.
  - No erroneously added components persist in the file.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to fix `ChartPage.tsx` after errors from `UI-CHARTPAGE-006` attempt.
  - `2024-07-27`: Replaced entire content of `ChartPage.tsx` with a fully corrected version, addressing state definitions, grid layout, checkbox labels, checkbox colors, and removing erroneous code.

### `UI-CHARTPAGE-008`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Fix checkbox display on smaller screens in `ChartPage` toolbar to prevent overlap.
- **Acceptance Criteria:**
  - On small (sm) screen sizes, the checkbox group does not overlap with the "OBS End" field.
  - The checkbox group correctly utilizes responsive grid settings (`xs={12} sm={12} md={2}`).
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to address responsive layout issue with checkboxes.
  - `2024-07-27`: Changed `sm` prop from `6` to `12` for the `Grid item` containing the checkboxes in `ChartPage.tsx`.

### `UI-CHARTPAGE-009`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Refine checkbox group layout in `ChartPage` toolbar for vertical stacking within its column, especially on `md` screens.
- **Acceptance Criteria:**
  - Checkboxes ("ED Required", "OBS Required") stack vertically within their `Grid item` (`md={2}`).
  - Checkboxes are left-aligned within their column.
  - Layout is consistent on `sm` screens (stacked and left-aligned).
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to address checkbox layout within their `md={2}` column.
  - `2024-07-27`: Changed `sx` prop of the checkbox `Grid item` to `{{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center' }}`.

### `UI-CHARTPAGE-010`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Fine-tune the vertical alignment of stacked checkboxes in `ChartPage` toolbar to ensure flush left alignment with fields above.
- **Acceptance Criteria:**
  - Stacked checkboxes ("ED Required", "OBS Required") are left-aligned without indentation relative to the `TextFields` in the same column structure.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to address slight rightward indentation of stacked checkboxes.
  - `2024-07-27`: Changed `justifyContent` from `'center'` to `'flex-start'` in the `sx` prop of the checkbox `Grid item`.

### `UI-CHARTPAGE-011`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Apply negative margin to `FormControlLabel` components for flush left alignment of checkboxes in `ChartPage` toolbar.
- **Acceptance Criteria:**
  - Stacked checkboxes ("ED Required", "OBS Required") are visually flush left-aligned with `TextFields` above/beside them, with no rightward indentation.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to fix slight indentation of stacked checkboxes.
  - `2024-07-27`: Added `marginLeft: '-11px'` to the `sx` prop of both `FormControlLabel` components for the checkboxes.

### `UI-CHARTPAGE-012`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Reduce negative margin for `FormControlLabel` components to correct checkbox alignment in `ChartPage` toolbar, preventing overlap.
- **Acceptance Criteria:**
  - Stacked checkboxes ("ED Required", "OBS Required") are visually left-aligned appropriately without overlapping the `TextField` to their left.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to fix overcorrection of checkbox alignment due to `-11px` margin.
  - `2024-07-27`: Changed `marginLeft` from `'-11px'` to `'-8px'` in the `sx` prop of both `FormControlLabel` components for the checkboxes.
  - `2024-07-27`: User manually updated `marginLeft` to `-2px` which was the optimal value.

### `UI-CHARTPAGE-013`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Modify `ChartPage` toolbar checkbox group to display side-by-side on `md` screens (and wider) and stack on `sm`/`xs` screens.
- **Acceptance Criteria:**
  - Checkboxes ("ED Required", "OBS Required") are in a single `Grid item` (`md={2}`).
  - On `md`+ screens, checkboxes appear side-by-side, distributed with space between them.
  - On `xs`/`sm` screens, checkboxes stack vertically and are aligned left/top.
  - Existing `marginLeft: '-2px'` on `FormControlLabel` components is preserved.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to make checkbox layout responsive.
  - `2024-07-27`: Applied responsive `flexDirection`, `alignItems`, and `justifyContent` to the `sx` prop of the parent `Grid item`. (Note: this led to label wrapping on `md` as `md` was `row`)

### `UI-CHARTPAGE-014`
- **Status:** `DONE`
- **Complexity:** Level 1
- **Requester:** User
- **Assignee:** AI Assistant
- **Description:** Force vertical stacking of checkboxes on `md` screens in `ChartPage` toolbar to prevent label wrapping.
- **Acceptance Criteria:**
  - Checkboxes ("ED Required", "OBS Required") stack vertically on `xs`, `sm`, and `md` screen sizes.
  - Labels do not wrap due to side-by-side placement on `md` screens.
- **Linked Files:** `[src/pages/ChartPage.tsx]`
- **History:**
  - `2024-07-27`: Task created to fix label wrapping issue from UI-CHARTPAGE-013.
  - `2024-07-27`: Changed `flexDirection`, `alignItems`, and `justifyContent` for the checkbox `Grid item` to ensure `column` behavior and `flex-start` alignment up to and including the `md` breakpoint.

---

### `TASK-APP-REFACTOR-001`
- **Status:** DONE
- **Complexity:** Level 3
- **Requester:** User
- **Assignee:** AI Assistant  
- **Description:** Comprehensive refactoring of App.tsx to implement performance optimizations, better error handling, improved authentication flows, and enhanced code organization.
- **Requirements:** 
  - Implement code splitting and lazy loading
  - Create reusable route configuration
  - Add proper error boundaries and loading states
  - Optimize theme provider structure
  - Extract authentication logic to custom hooks
  - Create enhanced error pages
  - Implement proper route guards
- **Acceptance Criteria:**
  1. ✅ Code splitting implemented with React.lazy for major components
  2. ✅ Route configuration extracted to separate file
  3. ✅ Error boundaries added with proper error pages
  4. ✅ Loading states implemented for authentication checks
  5. ✅ Authentication logic extracted to custom hooks
  6. ✅ Theme provider optimized to avoid duplication
  7. ✅ Proper 404 error page created
  8. ✅ Route guards implemented for protected routes
  9. ✅ Navigation utilities properly implemented with React Router hooks
  10. ✅ Production-ready logging (no console.log in production)
- **Linked Files:** 
  - ✅ `src/App.tsx` (completely refactored - main target)
  - ✅ `src/config/routes.ts` (new - centralized route configuration)
  - ✅ `src/shared/components/ErrorBoundary.tsx` (new - comprehensive error handling)
  - ✅ `src/shared/components/LoadingPage.tsx` (new - loading states)
  - ✅ `src/shared/components/NotFoundPage.tsx` (new - proper 404 page)
  - ✅ `src/features/auth/hooks/useAuthGuard.ts` (new - auth logic extraction)
  - ✅ `src/utils/navigation.ts` (new - navigation utilities)
- **History:**
  - `2025-01-27 00:00:00Z`: Task created.
  - `2025-01-27 00:00:01Z`: Started Phase 1 - Project Setup & Architecture.
  - `2025-01-27 00:00:02Z`: Completed Phase 1 - Created route config, error boundaries, loading/404 pages.
  - `2025-01-27 00:00:03Z`: Completed Phase 2 - Implemented code splitting and performance optimizations.
  - `2025-01-27 00:00:04Z`: Completed Phase 3 - Enhanced error handling with comprehensive error boundary.
  - `2025-01-27 00:00:05Z`: Completed Phase 4 - Extracted auth logic, created navigation utilities.
  - `2025-01-27 00:00:06Z`: Refactored App.tsx with all improvements. Application tested successfully.
  - `2025-01-27 00:00:07Z`: Task marked as DONE. All acceptance criteria met.