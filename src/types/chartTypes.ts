// TypeScript interfaces based on VB.NET BOChartInfo and BOChart classes

export interface BusinessObject {
  oid: number;
}

export interface BOChart extends BusinessObject {
  allocationsAsJson: string | null;
  chartInfo: number;
  configInstanceVersion: number;
  ecFormClassName: string | null;
  dateOfService: string; // Date
  creationDate: string | null; // Nullable Date
  modifiedDate: string | null; // Nullable Date
  isSeries: boolean;
  seriesOriginalChartID: string | null;
  visitID: string;
  treatmentArea: string | null;
  chartStatus: string | null;
  user: number;
  version: number;
  disposition: string | null;
  creationSource: string | null;
  patientFirstName: string | null;
  patientLastName: string | null;
  patientMiddleName: string | null;
  patientSuffix: string | null;
  mrn: string | null;
  triagePhysician: string | null;
  exportStatus: string | null;
  financialClass: string | null;
  hospitalService: string | null;
  edRequired: boolean;
  physChartStatus: string | null;
  physExportStatus: string | null;
  obsChartStatus: string | null;
  obsExportStatus: string | null;
  obsRequired: boolean;
  obsNurseUnit: string | null;
  facHasErrors: boolean | null;
  obsHasErrors: boolean | null;
  physHasErrors: boolean | null;
  edFacilityEM: string | null;
  edPhysicianEM: string | null;
  codingStatus: number;
  codedStatusNotes: string | null;
  payorRule: string | null;
  edFacilityException: string | null;
  edPhysicianException: string | null;
  aplResave: boolean;
  auditDate: string; // Date
  auditor: number;
  facChartStatusChangedByUser: number;
  facChartStatusChangedOn: string; // Date
  physChartStatusChangedByUser: number;
  physChartStatusChangedOn: string; // Date
  obsChartStatusChangedByUser: number;
  obsChartStatusChangedOn: string; // Date
  reviewNotes: string | null;
  obsReviewNotes: string | null;
  physReviewNotes: string | null;
  chartRedisplay?: Array<{
    chart: number;
    itemName: string;
    itemValue: string;
    oid: number;
  }>;
}

export interface BOChartInfo extends BusinessObject {
  miscDic: Record<string, any> | null;
  locked: boolean;
  lockStatus: string | null;
  lockUser: number;
  lockDate: string; // Date
  machineName: string | null;
  facility: number;
  visitID: string;
  chartVersion: number;
  chart: BOChart;
  isSeries: boolean;
  creationDate: string; // Date
  exportStatus: string | null;
  exportedChart: BOChart | null;
  adtOutOfSync: boolean;
  exportStatusChanged: string; // Date
  physExportStatusChanged: string; // Date
  physExportStatus: string | null;
  physExportChart: BOChart | null;
  cfExportFacilityChartVersion: BOChart | null;
  cfExportFacilityExportedDate: string; // Date
  cfExportFacilityStatus: string | null;
  cfExportInclude: boolean;
  cfExportPhysicianChartVersion: BOChart | null;
  cfExportPhysicianExportedDate: string; // Date
  cfExportPhysicianStatus: string | null;
  cfExportUserRequestPending: boolean;
  cfExportObservationChartVersion: BOChart | null;
  cfExportObservationExportedDate: string; // Date
  cfExportObservationStatus: string | null;
  obsExportChart: BOChart | null;
  obsExportStatus: string | null;
  obsExportStatusChanged: string; // Date
  userHasChangedEdData: boolean;
  userHasChangedObsData: boolean;
  userHasChangedPhysData: boolean;
  chartType: string | null;
  usesPoints: boolean;
}

export enum ChartTypes {
  ED = 'ED',
  Clinic = 'Clinic',
  ObsOnly = 'ObsOnly',
  UrgentCare = 'UrgentCare'
}

// Simplified interface for chart context state
export interface ChartContextState {
  chartInfo: BOChartInfo | null;
  isLoading: boolean;
  error: string | null;
  lastFetched: string | null;
}