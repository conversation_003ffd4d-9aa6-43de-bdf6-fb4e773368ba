import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider, StyledEngineProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { LicenseInfo } from '@mui/x-license-pro';

import App from './App.tsx';
import './global.css';
import theme from './theme';
import { AuthProvider } from './features/auth/contexts/AuthContext.tsx';
import { enableMocking } from './mocks';

async function prepareAndRender() {
  // Enable API mocking only if explicitly set
  if (import.meta.env.VITE_ENABLE_MOCKING === 'true') {
    await enableMocking();
  }

  // Create a client
  const queryClient = new QueryClient();

  // Set the license key, cleaning any potential whitespace
  const muiLicenseKey = import.meta.env.VITE_MUI_X_LICENSE_KEY?.trim();
  if (muiLicenseKey) {
    LicenseInfo.setLicenseKey(muiLicenseKey);
  } else {
    console.error('MUI X License key not found in environment variables. Please ensure VITE_MUI_X_LICENSE_KEY is set in your .env.production file.');
  }

  ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
      <BrowserRouter future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <StyledEngineProvider injectFirst>
              <ThemeProvider theme={theme}>
                <CssBaseline />
                <App />
              </ThemeProvider>
            </StyledEngineProvider>
          </AuthProvider>
        </QueryClientProvider>
      </BrowserRouter>
    </React.StrictMode>
  );
}

prepareAndRender();
