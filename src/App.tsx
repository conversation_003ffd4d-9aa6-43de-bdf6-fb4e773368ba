import { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Component imports
import SharedLayout from './shared/components/SharedLayout';
import ErrorBoundary from './shared/components/ErrorBoundary';
import LoadingPage from './shared/components/LoadingPage';

// Route configuration and auth
import { getProtectedRoutes, getPublicRoutes, NOT_FOUND_ROUTE, DEFAULT_PROTECTED_ROUTE } from './config/routes';
import { useAuthGuard } from './features/auth/hooks/useAuthGuard';

function App() {
  const { isLoading, shouldRedirectToLogin } = useAuthGuard();
  // Show loading state while authentication is being determined
  if (isLoading) {
    return <LoadingPage message="Checking authentication..." />;
  }
  // Render public routes for non-authenticated users
  if (shouldRedirectToLogin) {
    return (
      <ErrorBoundary>
        <Suspense fallback={<LoadingPage message="Loading page..." />}>
          <Routes>
            {getPublicRoutes().map((route) => (
              <Route 
                key={route.path}
                path={route.path} 
                element={<route.component />} 
              />
            ))}
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Suspense>
      </ErrorBoundary>
    );
  }
  // Render protected routes for authenticated users
  return (
    <ErrorBoundary>
      <SharedLayout>
        <Suspense fallback={<LoadingPage message="Loading page..." />}>
          <Routes>
            {/* Root redirect to default protected route */}
            <Route path="/" element={<Navigate to={DEFAULT_PROTECTED_ROUTE} replace />} />
            
            {/* Protected routes */}
            {getProtectedRoutes().map((route) => (
              <Route 
                key={route.path}
                path={route.path} 
                element={<route.component />} 
              />
            ))}
            
            {/* Not found route */}
            <Route path={NOT_FOUND_ROUTE.path} element={<NOT_FOUND_ROUTE.component />} />
          </Routes>
        </Suspense>
      </SharedLayout>
    </ErrorBoundary>
  );
}

export default App;
