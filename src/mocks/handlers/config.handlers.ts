import { http, HttpResponse } from 'msw'

// Mock configuration data
const mockConfigLists = {
  emergency: [
    {
      itemId: 'l1_initial_assessment',
      itemValue: 'Initial assessment',
      levelNumber: 1,
      description: 'Initial patient assessment and triage'
    },
    {
      itemId: 'l1_vital_signs',
      itemValue: 'Vital signs monitoring',
      levelNumber: 1,
      description: 'Basic vital signs monitoring'
    },
    {
      itemId: 'l2_tests_by_ed',
      itemValue: 'Tests by ED staff',
      levelNumber: 2,
      description: 'Diagnostic tests performed by ED staff'
    },
    {
      itemId: 'l2_medication_admin',
      itemValue: 'Medication administration',
      levelNumber: 2,
      description: 'Administration of medications'
    },
    {
      itemId: 'l3_complex_assessment',
      itemValue: 'Complex assessment',
      levelNumber: 3,
      description: 'Comprehensive patient assessment'
    },
    {
      itemId: 'l3_procedure_assistance',
      itemValue: 'Procedure assistance',
      levelNumber: 3,
      description: 'Assistance with medical procedures'
    },
    {
      itemId: 'l4_critical_care',
      itemValue: 'Critical care monitoring',
      levelNumber: 4,
      description: 'Intensive monitoring and care'
    },
    {
      itemId: 'l4_advanced_procedures',
      itemValue: 'Advanced procedures',
      levelNumber: 4,
      description: 'Complex medical procedures'
    },
    {
      itemId: 'l5_resuscitation',
      itemValue: 'Resuscitation efforts',
      levelNumber: 5,
      description: 'Emergency resuscitation procedures'
    },
    {
      itemId: 'l5_trauma_response',
      itemValue: 'Trauma response',
      levelNumber: 5,
      description: 'Major trauma response and care'
    }
  ],
  observation: [
    {
      itemId: 'obs_monitoring',
      itemValue: 'Patient monitoring',
      description: 'Continuous patient observation'
    },
    {
      itemId: 'obs_assessment',
      itemValue: 'Regular assessment',
      description: 'Periodic patient assessment'
    }
  ]
};

export const configHandlers = [
  // Config lists endpoint - handles multiple list names
  http.get('https://your-api-server.com/api/web/computed/Config/ConfigInstance/:configInstance/Lists', ({ request, params }) => {
    const url = new URL(request.url);
    const listNamesParam = url.searchParams.get('listNames');
    const configInstance = params.configInstance as string;
    
    if (!listNamesParam) {
      return HttpResponse.json(
        { error: 'listNames parameter is required' },
        { status: 400 }
      );
    }
    
    const requestedLists = listNamesParam.split(',').map(name => name.trim());
    const response: Record<string, any[]> = {};
    
    requestedLists.forEach(listName => {
      if (mockConfigLists[listName as keyof typeof mockConfigLists]) {
        response[listName] = mockConfigLists[listName as keyof typeof mockConfigLists];
      }
    });
    
    return HttpResponse.json(response);
  }),

  // Relative path version
  http.get('/web/computed/Config/ConfigInstance/:configInstance/Lists', ({ request, params }) => {
    const url = new URL(request.url);
    const listNamesParam = url.searchParams.get('listNames');
    const configInstance = params.configInstance as string;
    
    if (!listNamesParam) {
      return HttpResponse.json(
        { error: 'listNames parameter is required' },
        { status: 400 }
      );
    }
    
    const requestedLists = listNamesParam.split(',').map(name => name.trim());
    const response: Record<string, any[]> = {};
    
    requestedLists.forEach(listName => {
      if (mockConfigLists[listName as keyof typeof mockConfigLists]) {
        response[listName] = mockConfigLists[listName as keyof typeof mockConfigLists];
      }
    });
    
    return HttpResponse.json(response);
  }),

  // Individual config list endpoint
  http.get('https://your-api-server.com/api/web/computed/Config/ConfigInstance/:configInstance/List/:listName', ({ params }) => {
    const { configInstance, listName } = params as { configInstance: string; listName: string };
    
    if (mockConfigLists[listName as keyof typeof mockConfigLists]) {
      return HttpResponse.json({
        [listName]: mockConfigLists[listName as keyof typeof mockConfigLists]
      });
    }
    
    return HttpResponse.json(
      { error: `Configuration list '${listName}' not found` },
      { status: 404 }
    );
  }),

  // Relative path version
  http.get('/web/computed/Config/ConfigInstance/:configInstance/List/:listName', ({ params }) => {
    const { configInstance, listName } = params as { configInstance: string; listName: string };
    
    if (mockConfigLists[listName as keyof typeof mockConfigLists]) {
      return HttpResponse.json({
        [listName]: mockConfigLists[listName as keyof typeof mockConfigLists]
      });
    }
    
    return HttpResponse.json(
      { error: `Configuration list '${listName}' not found` },
      { status: 404 }
    );
  })
];
