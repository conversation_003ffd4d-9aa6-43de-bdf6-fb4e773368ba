import { http, HttpResponse } from 'msw'
import { mockFacilities, mockAplRecords } from '../data/apl.data'

export const aplHandlers = [
  // Handle both the full URL and the relative path
  http.get('https://localhost:44399/web/api/facility/GetFacilityDtos', () => {
    return HttpResponse.json(mockFacilities)
  }),

  http.get('/facility/GetFacilityDtos', () => {
    return HttpResponse.json(mockFacilities)
  }),

  http.get('https://localhost:44399/web/api/facility/:facilityId/aplRecords', ({ request, params }) => {
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '1000')
    const offset = parseInt(url.searchParams.get('offset') || '0')
    const fromDate = url.searchParams.get('fromDate')
    const toDate = url.searchParams.get('toDate')
    const visitId = url.searchParams.get('visitId')
    const mrn = url.searchParams.get('mrn')
    
    let records = [...mockAplRecords]
    
    if (visitId) {
      records = records.filter(r => r.visitId?.includes(visitId))
    }
    
    if (mrn) {
      records = records.filter(r => r.mrn?.includes(mrn))
    }
    
    if (fromDate) {
      records = records.filter(r => new Date(r.dos!) >= new Date(fromDate))
    }
    
    if (toDate) {
      records = records.filter(r => new Date(r.dos!) <= new Date(toDate))
    }
    
    const totalCount = records.length
    const paginatedRecords = records.slice(offset, offset + limit)
    
    return HttpResponse.json(paginatedRecords, {
      headers: {
        'x-total-count': totalCount.toString()
      }
    })
  }),

  http.get('/facility/:facilityId/aplRecords', ({ request, params }) => {
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '1000')
    const offset = parseInt(url.searchParams.get('offset') || '0')
    const fromDate = url.searchParams.get('fromDate')
    const toDate = url.searchParams.get('toDate')
    const visitId = url.searchParams.get('visitId')
    const mrn = url.searchParams.get('mrn')
    
    let records = [...mockAplRecords]
    
    if (visitId) {
      records = records.filter(r => r.visitId?.includes(visitId))
    }
    
    if (mrn) {
      records = records.filter(r => r.mrn?.includes(mrn))
    }
    
    if (fromDate) {
      records = records.filter(r => new Date(r.dos!) >= new Date(fromDate))
    }
    
    if (toDate) {
      records = records.filter(r => new Date(r.dos!) <= new Date(toDate))
    }
    
    const totalCount = records.length
    const paginatedRecords = records.slice(offset, offset + limit)
    
    return HttpResponse.json(paginatedRecords, {
      headers: {
        'x-total-count': totalCount.toString()
      }
    })
  }),

  http.get('https://localhost:44399/web/api/web/GlobalSetting/global', () => {
    return HttpResponse.json([
      {
        oid: '1',
        settingName: 'APP_VERSION',
        settingValue: '1.0.0-mock'
      },
      {
        oid: '2',
        settingName: 'FEATURE_FLAGS',
        settingValue: JSON.stringify({ mockMode: true })
      }
    ])
  }),

  http.get('/web/GlobalSetting/global', () => {
    return HttpResponse.json([
      {
        oid: '1',
        settingName: 'APP_VERSION',
        settingValue: '1.0.0-mock'
      },
      {
        oid: '2',
        settingName: 'FEATURE_FLAGS',
        settingValue: JSON.stringify({ mockMode: true })
      }
    ])
  })
]