import { lazy, type ComponentType } from 'react';

// Lazy load components for code splitting
const AplPage = lazy(() => import('../features/apl/components/AplPage'));
const ChartContainer = lazy(() => import('../features/chart/components/layout/ChartContainer'));
const ChartApiTest = lazy(() => import('../features/chart/components/utils/ChartApiTest'));
const LoginPage = lazy(() => import('../features/auth/components/LoginPage'));
const NotFoundPage = lazy(() => import('../shared/components/NotFoundPage'));

export interface RouteConfig {
  path: string;
  component: ComponentType;
  requiresAuth: boolean;
  title?: string;
  description?: string;
  exact?: boolean;
}

export const routes: RouteConfig[] = [
  {
    path: '/login',
    component: LoginPage,
    requiresAuth: false,
    title: 'Login',
    description: 'User authentication page'
  },
  {
    path: '/',
    component: AplPage,
    requiresAuth: true,
    title: 'Home',
    description: 'Default redirect to APL facility page',
    exact: true
  },
  {
    path: '/apl/facility',
    component: AplPage,
    requiresAuth: true,
    title: 'APL Facility',
    description: 'APL facility management page'
  },
  {
    path: '/apl/facility/:facilityId',
    component: AplPage,
    requiresAuth: true,
    title: 'APL Facility Details',
    description: 'Specific APL facility details page'
  },
  {
    path: '/visit/:visitId',
    component: ChartContainer,
    requiresAuth: true,
    title: 'Chart',
  },
  {
    path: '/test/chart-api',
    component: ChartApiTest,
    requiresAuth: true,
    title: 'Chart API Test',
    description: 'Test suite for chart API functionality'
  }
];

// Helper function to get protected routes
export const getProtectedRoutes = (): RouteConfig[] => {
  return routes.filter(route => route.requiresAuth);
};

// Helper function to get public routes
export const getPublicRoutes = (): RouteConfig[] => {
  return routes.filter(route => !route.requiresAuth);
};

// Helper function to find route by path
export const findRouteByPath = (path: string): RouteConfig | undefined => {
  return routes.find(route => route.path === path);
};

// Default redirect path for authenticated users
export const DEFAULT_PROTECTED_ROUTE = '/apl/facility';

// Not found route (special case)
export const NOT_FOUND_ROUTE: RouteConfig = {
  path: '*',
  component: NotFoundPage,
  requiresAuth: false,
  title: 'Page Not Found',
  description: '404 error page'
};
