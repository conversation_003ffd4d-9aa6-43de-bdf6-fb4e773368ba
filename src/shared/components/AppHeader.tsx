import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Icon<PERSON>utton,
  Typo<PERSON>,
  Box,
  Button,
  useTheme,
  useMediaQuery,
  Menu,
  MenuItem,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import LogoutIcon from '@mui/icons-material/Logout';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { useAuth } from '../../features/auth/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import React, { useEffect, useCallback } from 'react';

interface AppHeaderProps {
  onMenuClick: () => void;
  isHeaderMenuBackdropVisible: boolean;
  setIsHeaderMenuBackdropVisible: (isVisible: boolean) => void;
}

const TextNavItems = [
  { label: 'Maintenance', path: '/maintenance' },
  { label: 'Reports', path: '/reports' },
  { label: 'Settings', path: '/settings' },
  {
    label: 'Utils',
    path: '/utils',
    children: [
      { label: 'Test 1', path: '/utils/test1' },
      { label: 'Test 2', path: '/utils/test2' },
    ]
  },
  {
    label: 'Help',
    path: '/help',
    children: [
      {
        label: 'User Guides',
        path: '/user-guides',
        children: [
          { label: 'Document1', path: '/user-guides/doc1' },
          { label: 'Document2', path: '/user-guides/doc2' },
        ]
      },
      { label: 'Client Portal', path: '/client-portal' },
    ]
  },
];

const ContainedNavItems = [
  { label: 'Alias', path: '/alias' },
  { label: 'Data Requester', path: '/data-requester' },
];

const AppHeader = ({ 
  onMenuClick, 
  isHeaderMenuBackdropVisible, 
  setIsHeaderMenuBackdropVisible 
}: AppHeaderProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [openMenu, setOpenMenu] = React.useState<string | null>(null);
  const [nestedAnchorEl, setNestedAnchorEl] = React.useState<null | HTMLElement>(null);
  const [openNestedMenuLabel, setOpenNestedMenuLabel] = React.useState<string | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, menuLabel: string) => {
    setAnchorEl(event.currentTarget);
    setOpenMenu(menuLabel);
    setIsHeaderMenuBackdropVisible(true);
  };

  const handleNestedMenuClose = useCallback(() => {
    setNestedAnchorEl(null);
    setOpenNestedMenuLabel(null);
  }, []);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setOpenMenu(null);
    handleNestedMenuClose();
    setIsHeaderMenuBackdropVisible(false);
  }, [handleNestedMenuClose, setIsHeaderMenuBackdropVisible]);

  const handleNestedMenuOpen = (event: React.MouseEvent<HTMLElement>, nestedMenuLabel: string) => {
    event.stopPropagation();
    if (nestedAnchorEl && openNestedMenuLabel !== nestedMenuLabel) {
        handleNestedMenuClose();
    }
    setNestedAnchorEl(event.currentTarget);
    setOpenNestedMenuLabel(nestedMenuLabel);
  };

  const handleNavigation = (path: string, label: string) => {
    console.log(`Navigate to ${path} for ${label}`);
    // Special case for Test 1 - navigate to chart API test
    if (label === 'Test 1') {
      navigate('/test/chart-api');
    } else {
      // For other items, just log for now
      console.log(`Navigation to ${path} not implemented yet`);
    }
    handleMenuClose();
  };

  useEffect(() => {
    if (!isHeaderMenuBackdropVisible) {
      if (openMenu || openNestedMenuLabel) {
        handleMenuClose();
      }
    }
  }, [isHeaderMenuBackdropVisible, handleMenuClose, openMenu, openNestedMenuLabel]);

  return (
    <AppBar 
      position="sticky"
      color="primary"
      sx={{ 
        top: 0,
        zIndex: theme.zIndex.drawer + 2,
        backgroundColor: theme.palette.primary.main,
      }}
    >
      <Toolbar 
        variant="dense"
        sx={{
          color: 'white',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={onMenuClick}
              sx={{ mr: 1 }}
            >
              <MenuIcon />
            </IconButton>

            {!isMobile && (
              <>
                {TextNavItems.map((item, index) => (
                  item.children ? (
                    <React.Fragment key={item.label}>
                      <Button
                        aria-controls={openMenu === item.label ? `${item.label}-menu` : undefined}
                        aria-haspopup="true"
                        aria-expanded={openMenu === item.label ? 'true' : undefined}
                        onClick={(e) => handleMenuOpen(e, item.label)}
                        sx={{ textTransform: 'none', mr: 1, ...(index === 0 && { ml: 1 }), color: 'inherit' }}
                      >
                        <Typography variant="body1" color="inherit">
                          {item.label}
                        </Typography>
                      </Button>
                      <Menu
                        id={`${item.label}-menu`}
                        anchorEl={anchorEl}
                        open={openMenu === item.label}
                        onClose={handleMenuClose}
                        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
                        transformOrigin={{ vertical: 'top', horizontal: 'center' }}
                        PaperProps={{
                          sx: {
                            overflow: 'visible',
                            mt: 1.5,
                            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                            '&::before': {
                              content: '""',
                              position: 'absolute',
                              top: 0,
                              left: '50%',
                              transform: 'translate(-50%, -100%)',
                              width: 0,
                              height: 0,
                              borderLeft: '8px solid transparent',
                              borderRight: '8px solid transparent',
                              borderBottom: `8px solid ${theme.palette.background.paper}`,
                            },
                          }
                        }}
                      >
                        {item.children.map((childItem) => (
                          childItem.children ? (
                            <React.Fragment key={childItem.label}>
                              <MenuItem
                                onClick={(e) => handleNestedMenuOpen(e, childItem.label)}
                                sx={{ display: 'flex', justifyContent: 'space-between' }}
                              >
                                {childItem.label}
                                <ChevronRightIcon fontSize="small" />
                              </MenuItem>
                              <Menu
                                id={`${item.label}-${childItem.label}-submenu`}
                                anchorEl={nestedAnchorEl}
                                open={openNestedMenuLabel === childItem.label && Boolean(nestedAnchorEl)}
                                onClose={() => {
                                  handleNestedMenuClose();
                                }}
                                MenuListProps={{ onMouseLeave: handleNestedMenuClose }}
                                anchorOrigin={{
                                  vertical: 'top',
                                  horizontal: 'right',
                                }}
                                transformOrigin={{
                                  vertical: 'top',
                                  horizontal: 'left',
                                }}
                                PaperProps={{
                                  sx: {
                                    overflow: 'visible',
                                    ml: 1.5,
                                    filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                                    '&::before': {
                                      content: '""',
                                      position: 'absolute',
                                      top: '10px',
                                      left: 0,
                                      transform: 'translate(-100%, 0)',
                                      width: 0,
                                      height: 0,
                                      borderTop: '8px solid transparent',
                                      borderBottom: '8px solid transparent',
                                      borderRight: `8px solid ${theme.palette.background.paper}`,
                                    },
                                  }
                                }}
                              >
                                {childItem.children.map((grandChildItem) => (
                                  <MenuItem
                                    key={grandChildItem.label}
                                    onClick={() => {
                                      console.log(`Navigate to ${grandChildItem.path}`);
                                      handleNestedMenuClose();
                                      handleMenuClose();
                                    }}
                                  >
                                    {grandChildItem.label}
                                  </MenuItem>
                                ))}
                              </Menu>
                            </React.Fragment>
                          ) : (
                            <MenuItem
                              key={childItem.label}
                              onClick={() => handleNavigation(childItem.path, childItem.label)}
                            >
                              {childItem.label}
                            </MenuItem>
                          )
                        ))}
                      </Menu>
                    </React.Fragment>
                  ) : (
                    <Button
                      key={item.label}
                      // onClick={() => console.log(`Navigate to ${item.path}`)} // Placeholder
                      sx={{ textTransform: 'none', mr: 1, ...(index === 0 && { ml: 1 }), color: 'inherit' }}
                    >
                      <Typography variant="body1" color="inherit">
                        {item.label}
                      </Typography>
                    </Button>
                  )
                ))}
                {ContainedNavItems.map((item) => (
                  <Button
                    key={item.label}
                    variant="outlined"
                    size="small"
                    disabled
                    sx={{
                      mr: 1,
                      borderColor: 'rgba(255, 255, 255, 0.5)',
                      color: 'inherit',
                      '&.Mui-disabled': {
                        borderColor: 'rgba(255, 255, 255, 0.3)',
                        color: 'rgba(255, 255, 255, 0.3)',
                      }
                    }}
                  >
                    {item.label}
                  </Button>
                ))}
              </>
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="caption" sx={{ mr: 1 }} color="inherit">
              v0.1
            </Typography>
            
            {user && (
              <Typography variant="body2" sx={{ mr: 2, display: { xs: 'none', md: 'block' } }} color="inherit">
                {user.displayName || user.username}
              </Typography>
            )}
            {user && (
              <IconButton
                color="inherit"
                onClick={logout}
                size="small"
                sx={{ ml: 1, display: { xs: 'none', md: 'block' } }}
              >
                <LogoutIcon />
              </IconButton>
            )}
          </Box>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader;
