import axios from 'axios';
import { getAuthToken } from '../../features/auth/services/tokenManager'; // Removed setAuthToken as it's handled by AuthContext now
import { dispatchLogoutEvent } from '../../features/auth/services/authEventManager'; // Import the event dispatcher

// You might want to set a VITE_APP_API_BASE_URL in your .env file
const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL || 'https://your-api-server.com/api'; // Ensure this is set in .env

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json; charset=utf-8',
    'Accept': 'application/json',
  },
  decompress: false, // Disable automatic decompression
});

// Request interceptor to add the auth token to headers
axiosInstance.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// We will add response interceptors later for 401 handling (AUTH-006.4)

// Response interceptor for API errors, specifically 401 Unauthorized
axiosInstance.interceptors.response.use(
  (response) => {
    return response; // Pass through successful responses
  },
  (error) => {
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 401) {
        console.error('Axios Interceptor: Received 401 Unauthorized. Dispatching logout event.');
        
        // Dispatch a logout event. AuthContext will handle the rest.
        dispatchLogoutEvent();
        
        // No longer directly manipulating token or redirecting here
        // setAuthToken(null); 
        // window.location.href = '/'; 
        
        return Promise.reject(new Error('Session expired or unauthorized. Please login again.'));
      }
    }
    return Promise.reject(error); // Pass through other errors
  }
);

export default axiosInstance;
