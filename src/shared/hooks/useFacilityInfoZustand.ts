/**
 * useFacilityInfo Hook (Zustand Version)
 * 
 * Backward compatible hook that provides the same API as the original
 * useFacilityInfo but powered by <PERSON>ustand instead of React Context.
 * 
 * Benefits over Context version:
 * - Better performance with granular subscriptions
 * - Persistent selected facility across sessions
 * - No Provider required
 * - Better DevTools support
 * - Type-safe state management
 * 
 * Usage examples:
 * - Check if facility has pending configuration: `facilityInfo?.hasPendingConfig`
 * - Get facility configuration OIDs: `facilityInfo?.configInstanceOid`
 * - Access facility identifiers: `facilityInfo?.oid`
 * - Check if facility is default: `facilityInfo?.isDefault`
 */

import { useFacilityStore, facilitySelectors } from '../stores/facilityStore';
import type { FacilityListItem } from '../../features/apl/services/facilityService';

export interface UseFacilityInfoReturn {
  /** Full facility data with all API fields for decision making */
  facilityInfo: FacilityListItem | null;
  
  /** Check if facility has pending configuration */
  hasPendingConfig: boolean;
  
  /** Check if this is the default facility */
  isDefault: boolean;
  
  /** Get the facility OID for API calls */
  oid: number | null;
  
  /** Get configuration instance OID if available */
  configInstanceOid: number | null;
  
  /** Get pending configuration instance OID if available */
  pendingConfigInstanceOid: number | null;
  
  /** Get shared configuration instance OID if available */
  sharedConfigInstanceOid: number | null;
}

/**
 * Hook that provides facility information with the same API as the original
 * but powered by Zustand for better performance and persistence.
 * 
 * This hook is a drop-in replacement for the original useFacilityInfo.
 */
export const useFacilityInfo = (): UseFacilityInfoReturn => {
  // Get the selected facility from Zustand store
  const selectedFacilityFull = useFacilityStore(state => state.selectedFacilityFull);
  
  // Use selectors for optimized computed values
  const hasPendingConfig = useFacilityStore(facilitySelectors.hasSelectedFacilityPendingConfig);
  const isDefault = useFacilityStore(facilitySelectors.isSelectedFacilityDefault);
  const oid = useFacilityStore(facilitySelectors.getSelectedFacilityOid);
  const configInstanceOid = useFacilityStore(facilitySelectors.getSelectedFacilityConfigInstanceOid);
  const pendingConfigInstanceOid = useFacilityStore(facilitySelectors.getSelectedFacilityPendingConfigInstanceOid);
  const sharedConfigInstanceOid = useFacilityStore(facilitySelectors.getSelectedFacilitySharedConfigInstanceOid);

  return {
    facilityInfo: selectedFacilityFull,
    hasPendingConfig,
    isDefault,
    oid,
    configInstanceOid,
    pendingConfigInstanceOid,
    sharedConfigInstanceOid,
  };
};

/**
 * Additional utility hooks for more specific use cases
 */

/**
 * Hook that only returns the selected facility OID
 * Useful for components that only need the facility identifier
 */
export const useFacilityOid = (): number | null => {
  return useFacilityStore(facilitySelectors.getSelectedFacilityOid);
};

/**
 * Hook that only returns the config instance OID
 * Useful for config-related components
 */
export const useConfigInstanceOid = (): number | null => {
  return useFacilityStore(facilitySelectors.getSelectedFacilityConfigInstanceOid);
};

/**
 * Hook that returns facility selection state
 * Useful for conditional rendering
 */
export const useFacilitySelected = (): boolean => {
  return useFacilityStore(facilitySelectors.hasSelectedFacility);
};

/**
 * Hook that returns facility name
 * Useful for display components
 */
export const useFacilityName = (): string | null => {
  return useFacilityStore(facilitySelectors.getSelectedFacilityName);
};

/**
 * Hook that returns the facility setter function
 * Useful for components that need to update the selected facility
 */
export const useFacilityActions = () => {
  const setSelectedFacilityFull = useFacilityStore(state => state.setSelectedFacilityFull);
  const setFacilities = useFacilityStore(state => state.setFacilities);
  const setLoading = useFacilityStore(state => state.setLoading);
  const setError = useFacilityStore(state => state.setError);
  const clearFacilityState = useFacilityStore(state => state.clearFacilityState);
  const getFacilityById = useFacilityStore(state => state.getFacilityById);

  return {
    setSelectedFacilityFull,
    setFacilities,
    setLoading,
    setError,
    clearFacilityState,
    getFacilityById,
  };
};

/**
 * Hook that returns all facility store state and actions
 * Useful for components that need full store access
 * 
 * Note: Use the store import directly from facilityStore.ts
 * import { useFacilityStore } from '../stores/facilityStore';
 */

/**
 * Default export maintains backward compatibility
 */
export default useFacilityInfo;