// Centralized hooks for loading configuration lists with facility integration
// Automatically uses facility context to get configInstance

import { useQuery } from '@tanstack/react-query';
import { useFacilityInfo } from './useFacilityInfoZustand';
import { configService, type ConfigListsResponse, type ConfigList } from '../services/configService';
import { logger } from '../../utils/logger';

// Hook options interface
export interface UseConfigListsOptions {
  enabled?: boolean;
  staleTime?: number;
  gcTime?: number;
  retry?: number;
}

// Default options
const DEFAULT_OPTIONS: Required<UseConfigListsOptions> = {
  enabled: true,
  staleTime: 300000, // 5 minutes
  gcTime: 600000, // 10 minutes
  retry: 1
};

/**
 * Hook to fetch a single configuration list using facility context
 * @param listName - Name of the configuration list to fetch
 * @param options - Query options
 * @returns React Query result with configuration list data
 */
export const useConfigList = (
  listName: string,
  options: UseConfigListsOptions = {}
) => {
  const { configInstanceOid } = useFacilityInfo();
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };

  return useQuery<ConfigList, Error>({
    queryKey: ['configList', configInstanceOid, listName],
    queryFn: async () => {
      if (!configInstanceOid) {
        throw new Error('No configuration instance available from facility context');
      }

      logger.info('Fetching single config list with facility context', {
        configInstanceOid,
        listName
      });

      return configService.fetchConfigList(configInstanceOid, listName);
    },
    enabled: Boolean(configInstanceOid) && Boolean(listName) && mergedOptions.enabled,
    staleTime: mergedOptions.staleTime,
    gcTime: mergedOptions.gcTime,
    retry: mergedOptions.retry,
  });
};

/**
 * Hook to fetch multiple configuration lists using facility context
 * @param listNames - Array of configuration list names to fetch
 * @param options - Query options
 * @returns React Query result with multiple configuration lists
 */
export const useConfigLists = (
  listNames: string[],
  options: UseConfigListsOptions = {}
) => {
  const { configInstanceOid } = useFacilityInfo();
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };

  // Sort list names for consistent query key
  const sortedListNames = [...listNames].sort();

  return useQuery<ConfigListsResponse, Error>({
    queryKey: ['configLists', configInstanceOid, sortedListNames.join(',')],
    queryFn: async () => {
      if (!configInstanceOid) {
        throw new Error('No configuration instance available from facility context');
      }

      logger.info('Fetching multiple config lists with facility context', {
        configInstanceOid,
        listNames: sortedListNames
      });

      return configService.fetchConfigLists(configInstanceOid, sortedListNames);
    },
    enabled: Boolean(configInstanceOid) && listNames.length > 0 && mergedOptions.enabled,
    staleTime: mergedOptions.staleTime,
    gcTime: mergedOptions.gcTime,
    retry: mergedOptions.retry,
  });
};

/**
 * Hook to fetch configuration lists with automatic facility context and validation
 * Provides additional error handling and data transformation
 * @param listNames - Array of configuration list names to fetch
 * @param options - Query options with additional validation settings
 * @returns Enhanced query result with validation and transformation
 */
export const useValidatedConfigLists = (
  listNames: string[],
  options: UseConfigListsOptions & { 
    validateAll?: boolean;
    transformLists?: boolean;
  } = {}
) => {
  const { configInstanceOid, facilityInfo } = useFacilityInfo();
  const { validateAll = true, transformLists = true, ...queryOptions } = options;
  const mergedOptions = { ...DEFAULT_OPTIONS, ...queryOptions };

  // Sort list names for consistent query key
  const sortedListNames = [...listNames].sort();

  return useQuery<ConfigListsResponse, Error>({
    queryKey: ['validatedConfigLists', configInstanceOid, sortedListNames.join(','), validateAll, transformLists],
    queryFn: async () => {
      if (!configInstanceOid) {
        throw new Error('No configuration instance available from facility context');
      }

      if (!facilityInfo) {
        throw new Error('No facility information available');
      }

      logger.info('Fetching validated config lists', {
        configInstanceOid,
        facilityOid: facilityInfo.oid,
        listNames: sortedListNames,
        validateAll,
        transformLists
      });

      const response = await configService.fetchConfigLists(configInstanceOid, sortedListNames);

      // Validate that all requested lists are present
      if (validateAll) {
        configService.validateConfigResponse(response, sortedListNames);
      }

      // Transform lists if requested
      if (transformLists) {
        response.lists = configService.transformConfigLists(response.lists);
      }

      return response;
    },
    enabled: Boolean(configInstanceOid) && Boolean(facilityInfo) && listNames.length > 0 && mergedOptions.enabled,
    staleTime: mergedOptions.staleTime,
    gcTime: mergedOptions.gcTime,
    retry: mergedOptions.retry,
  });
};

/**
 * Hook for fetching EM level configuration lists by exact list name
 * @param listName - Exact name of the EM level config list (e.g., 'em_levels_emergency', 'em_levels_observation')
 * @param options - Query options
 * @returns Query result with EM level configuration
 */
export const useEmLevelConfig = (
  listName: string,
  options: UseConfigListsOptions = {}
) => {
  return useConfigList(listName, {
    ...options,
    // EM levels might change more frequently, so shorter stale time
    staleTime: 180000, // 3 minutes
    ...options
  });
};

/**
 * Hook for fetching multiple EM level configurations at once
 * @param listNames - Array of exact EM level config list names
 * @param options - Query options
 * @returns Query result with multiple EM level configurations
 */
export const useMultipleEmLevelConfigs = (
  listNames: string[],
  options: UseConfigListsOptions = {}
) => {
  return useConfigLists(listNames, {
    ...options,
    // EM levels might change more frequently, so shorter stale time
    staleTime: 180000, // 3 minutes
    ...options
  });
};

/**
 * Utility hook to get facility configuration status
 * @returns Object with facility configuration information
 */
export const useFacilityConfigStatus = () => {
  const { configInstanceOid, facilityInfo, hasPendingConfig } = useFacilityInfo();

  return {
    hasConfigInstance: Boolean(configInstanceOid),
    configInstanceOid,
    facilityOid: facilityInfo?.oid || null,
    facilityName: facilityInfo?.longName || null,
    hasPendingConfig,
    isReady: Boolean(configInstanceOid && facilityInfo),
    canLoadConfig: Boolean(configInstanceOid)
  };
};

// Export all hooks as a group for easier imports
export const configHooks = {
  useConfigList,
  useConfigLists,
  useValidatedConfigLists,
  useEmLevelConfig,
  useMultipleEmLevelConfigs,
  useFacilityConfigStatus
};

export default configHooks;