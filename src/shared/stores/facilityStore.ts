/**
 * Facility Store (Zustand)
 * 
 * Replaces AppContext for facility state management with improved performance,
 * persistence, and developer experience.
 * 
 * Features:
 * - Persistent selected facility across sessions
 * - Better performance with granular subscriptions
 * - Type-safe state management
 * - DevTools support
 * - Backward compatible with existing useFacilityInfo API
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { FacilityListItem } from '../../features/apl/services/facilityService';

/**
 * Facility store state interface
 */
interface FacilityState {
  /** Currently selected facility (full object) */
  selectedFacilityFull: FacilityListItem | null;
  
  /** Facility list for future use (could be populated from API) */
  facilities: FacilityListItem[];
  
  /** Loading state for facility operations */
  isLoading: boolean;
  
  /** Error state for facility operations */
  error: string | null;
}

/**
 * Facility store actions interface
 */
interface FacilityActions {
  /**
   * Set the selected facility (maintains same API as AppContext)
   * @param facility - The facility to select, or null to clear
   */
  setSelectedFacilityFull: (facility: FacilityListItem | null) => void;
  
  /**
   * Set the facilities list (for future use)
   * @param facilities - Array of available facilities
   */
  setFacilities: (facilities: FacilityListItem[]) => void;
  
  /**
   * Set loading state
   * @param loading - Whether facility operations are in progress
   */
  setLoading: (loading: boolean) => void;
  
  /**
   * Set error state
   * @param error - Error message or null to clear
   */
  setError: (error: string | null) => void;
  
  /**
   * Clear all facility state
   */
  clearFacilityState: () => void;
  
  /**
   * Get facility by ID (for future use)
   * @param facilityId - The facility ID to find
   * @returns The facility or null if not found
   */
  getFacilityById: (facilityId: string) => FacilityListItem | null;
}

/**
 * Complete facility store type
 */
export type FacilityStore = FacilityState & FacilityActions;

/**
 * Zustand facility store with persistence
 */
export const useFacilityStore = create<FacilityStore>()(
  persist(
    (set, get) => ({
      // State
      selectedFacilityFull: null,
      facilities: [],
      isLoading: false,
      error: null,
      
      // Actions
      setSelectedFacilityFull: (facility) => {
        set({ 
          selectedFacilityFull: facility,
          error: null // Clear any previous errors when setting facility
        });
      },
      
      setFacilities: (facilities) => {
        set({ facilities });
      },
      
      setLoading: (loading) => {
        set({ isLoading: loading });
      },
      
      setError: (error) => {
        set({ error });
      },
      
      clearFacilityState: () => {
        set({
          selectedFacilityFull: null,
          facilities: [],
          isLoading: false,
          error: null,
        });
      },
      
      getFacilityById: (facilityId) => {
        const state = get();
        return state.facilities.find(f => f.facilityID === facilityId) || null;
      },
    }),
    {
      name: 'facility-storage', // localStorage key
      
      // Only persist the selected facility, not loading states or facility list
      partialize: (state) => ({
        selectedFacilityFull: state.selectedFacilityFull,
      }),
      
      // Version for future migrations
      version: 1,
    }
  )
);

/**
 * Selectors for computed values
 * These provide optimized access to derived state
 */
export const facilitySelectors = {
  /**
   * Check if a facility is currently selected
   */
  hasSelectedFacility: (state: FacilityStore): boolean => 
    state.selectedFacilityFull !== null,
  
  /**
   * Get the selected facility's OID
   */
  getSelectedFacilityOid: (state: FacilityStore): number | null =>
    state.selectedFacilityFull?.oid ?? null,
  
  /**
   * Get the selected facility's ID
   */
  getSelectedFacilityId: (state: FacilityStore): string | null =>
    state.selectedFacilityFull?.facilityID ?? null,
  
  /**
   * Get the selected facility's name
   */
  getSelectedFacilityName: (state: FacilityStore): string | null =>
    state.selectedFacilityFull?.longName ?? null,
  
  /**
   * Check if selected facility has pending configuration
   */
  hasSelectedFacilityPendingConfig: (state: FacilityStore): boolean =>
    state.selectedFacilityFull?.hasPendingConfig ?? false,
  
  /**
   * Check if selected facility is default
   */
  isSelectedFacilityDefault: (state: FacilityStore): boolean =>
    state.selectedFacilityFull?.isDefault ?? false,
  
  /**
   * Get config instance OID for selected facility
   */
  getSelectedFacilityConfigInstanceOid: (state: FacilityStore): number | null =>
    state.selectedFacilityFull?.configInstanceOid ?? null,
  
  /**
   * Get pending config instance OID for selected facility
   */
  getSelectedFacilityPendingConfigInstanceOid: (state: FacilityStore): number | null =>
    state.selectedFacilityFull?.pendingConfigInstanceOid ?? null,
  
  /**
   * Get shared config instance OID for selected facility
   */
  getSelectedFacilitySharedConfigInstanceOid: (state: FacilityStore): number | null =>
    state.selectedFacilityFull?.sharedConfigInstanceOid ?? null,
};

/**
 * Default export for backward compatibility
 */
export default useFacilityStore;