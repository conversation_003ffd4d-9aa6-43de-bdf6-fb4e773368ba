/**
 * useAplSearch Hook
 * 
 * Custom React hook that manages APL (Active Patient List) search functionality.
 * Uses React Query for data fetching with background refresh capabilities to keep
 * data current without showing loading indicators to users.
 * 
 * Key concepts for React/TS newcomers:
 * - useQuery: React Query hook for data fetching with caching and background updates
 * - useCallback: Memoizes functions to prevent recreation on every render
 * - useMemo: Memoizes the return object to prevent unnecessary re-renders
 * - Background refetching: Keeps data fresh without interrupting user experience
 */
import { useState, useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { aplRecordsApi } from '../services/aplRecordsService';
import type { AplRecord } from '../services/aplRecordsService';
import { useAuth } from '../../auth/contexts/AuthContext';
import { startOfDay, endOfDay, format } from 'date-fns';

export interface AplSearchFilters {
  facilityId: string;
  fromDate: Date | null;
  toDate: Date | null;
  visitId?: string;
  mrn?: string;
}

export interface UseAplSearchReturn {
  aplRecords: AplRecord[];
  totalRecords: number;
  isLoading: boolean;
  isFetching: boolean;
  error: string | null;
  loadAplRecords: (filters?: AplSearchFilters) => Promise<void>;
  refreshAplRecords: () => Promise<void>;
  handleFilterChange: () => void;
}

export const useAplSearch = (): UseAplSearchReturn => {
  const { accessToken } = useAuth();
  const [currentFilters, setCurrentFilters] = useState<AplSearchFilters | null>(null);

  // Validate dates
  const isValidDateRange = useCallback((date: Date | null): boolean => {
    if (!date) return true; // null is valid (no date filter)
    if (isNaN(date.getTime())) return false; // Invalid Date object
    const year = date.getFullYear();
    return year >= 1900 && year <= 2100; // Reasonable date range
  }, []);

  // Validate filters
  const validateFilters = useCallback((filters: AplSearchFilters | null): string | null => {
    if (!filters) return null;
    
    if (!filters.facilityId) {
      return "Please select a facility before searching.";
    }

    if (filters.fromDate && !isValidDateRange(filters.fromDate)) {
      return "Invalid 'From' date. Please enter a date between 1900 and 2100.";
    }

    if (filters.toDate && !isValidDateRange(filters.toDate)) {
      return "Invalid 'To' date. Please enter a date between 1900 and 2100.";
    }

    return null;
  }, [isValidDateRange]);

  // React Query for APL records
  const {
    data: queryData,
    isLoading,
    isFetching,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ['aplRecords', currentFilters],
    queryFn: async () => {
      if (!currentFilters) return { results: [], total: 0 };
      
      // Format dates to include time information for proper filtering using date-fns
      const fromDateParam = currentFilters.fromDate
        ? format(startOfDay(currentFilters.fromDate), "yyyy-MM-dd'T'HH:mm:ss")
        : undefined;
      const toDateParam = currentFilters.toDate
        ? format(endOfDay(currentFilters.toDate), "yyyy-MM-dd'T'HH:mm:ss")
        : undefined;
      
      return await aplRecordsApi.getAplRecords({
        facilityId: currentFilters.facilityId,
        fromDate: fromDateParam,
        toDate: toDateParam,
        visitId: currentFilters.visitId || undefined,
        mrn: currentFilters.mrn || undefined,
        limit: 1000
      });
    },
    enabled: !!accessToken && !!currentFilters && !validateFilters(currentFilters),
    staleTime: 60000, // 1 minute - APL data changes more frequently than facilities
    gcTime: 300000, // Keep in cache for 5 minutes (formerly cacheTime)
    refetchInterval: 60000, // Check for changes every minute in background
    refetchIntervalInBackground: true, // Continue checking when tab is inactive
    refetchOnWindowFocus: true, // Check when user returns to the tab
    refetchOnReconnect: true, // Check when network reconnects
    retry: 1,
  });

  // Handle validation errors
  const validationError = validateFilters(currentFilters);
  const error = validationError || (queryError?.message || null);

  // Fetch function that updates filters and triggers query
  const loadAplRecords = useCallback(async (filters?: AplSearchFilters) => {
    if (!filters) return;
    
    const validationError = validateFilters(filters);
    if (validationError) {
      return;
    }

    setCurrentFilters(filters);
  }, [validateFilters]);

  // Manual refresh function using current filters - forces immediate backend call
  const refreshAplRecords = useCallback(async () => {
    if (currentFilters && !validateFilters(currentFilters)) {
      // Force refetch regardless of stale time for manual refresh
      await refetch({ cancelRefetch: false });
    }
  }, [currentFilters, refetch, validateFilters]);

  const handleFilterChange = useCallback(() => {
    // Currently empty, but memoized for performance
    // This could be extended to handle grid-level filtering
  }, []);

  return useMemo(() => {
    // Extract data with fallbacks inside useMemo to prevent dependency issues
    const aplRecords = queryData?.results || [];
    const totalRecords = queryData?.total || 0;

    return {
      aplRecords,
      totalRecords,
      isLoading,
      isFetching,
      error,
      loadAplRecords,
      refreshAplRecords,
      handleFilterChange,
    };
  }, [
    queryData?.results,
    queryData?.total,
    isLoading,
    isFetching,
    error,
    loadAplRecords,
    refreshAplRecords,
    handleFilterChange,
  ]);
};
