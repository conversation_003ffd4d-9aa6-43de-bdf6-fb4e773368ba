import React from 'react';
import axiosInstance from '../../../shared/services/axiosInstance';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../auth/contexts/AuthContext';

// Define an Interface for the facility configuration
export interface FacilityListItem {
  facilityID: string;
  longName: string;
  // comments: string;
  // edContact: string;
  // techContact: string;
  // companyClient: string;
  hasPendingConfig: boolean;
  oid: number;
  isDefault: boolean;
  pendingConfigInstanceOid?: number; // Optional field for pending config instance OID
  configInstanceOid?: number; // Optional field for config instance OID
  sharedConfigInstanceOid?: number; // Optional field for shared config instance OID

}

// Create an API Service Function to fetch facility configuration
export const fetchFacilityListItems = async (): Promise<FacilityListItem[]> => {
  console.log('fetchFacilityConfiguration: Function called, attempting API request to /GetFacilityDtos.');
  try {
    // The endpoint is now /GetFacilityDtos (base URL already includes web/api).
    // Assuming 'api' is part of the base URL configured in axiosInstance.
    const response = await axiosInstance.get<FacilityListItem[]>('/facility/GetFacilityDtos');
    
    // Debug: Log the response details
    console.log('API Response Status:', response.status);
    console.log('API Response Headers:', response.headers);
    console.log('API Response Data Type:', typeof response.data);
    console.log('API Response Data:', response.data);
    
    // Debug: Check configInstanceOid values specifically
    if (Array.isArray(response.data)) {
      console.log('Facility configInstanceOid values:', 
        response.data.map(f => ({ 
          facilityID: f.facilityID, 
          configInstanceOid: f.configInstanceOid,
          pendingConfigInstanceOid: f.pendingConfigInstanceOid,
          sharedConfigInstanceOid: f.sharedConfigInstanceOid
        }))
      );
    }
      // Check if response is HTML (indicates an error/redirect)
    if (typeof response.data === 'string' && (response.data as string).includes('<!doctype html>')) {
      throw new Error('API returned HTML instead of JSON - possible authentication or routing issue');
    }
    
    return response.data;
  } catch (error) {
    console.error('Error fetching facility list items:', error);
    // You might want to throw a custom error or handle it more specifically
    throw error;
  }
};

// React Query hook for fetching facility list items
export const useFacilitiesQuery = () => {
  const { accessToken } = useAuth(); // Get the accessToken
  
  // Performance tracking - only log when access token presence changes
  const prevAccessToken = React.useRef(!!accessToken);
  React.useEffect(() => {
    const hasAccessToken = !!accessToken;
    if (prevAccessToken.current !== hasAccessToken) {
      console.log('[PERF] useFacilitiesQuery executed. AccessToken present:', prevAccessToken.current, '->', hasAccessToken);
      prevAccessToken.current = hasAccessToken;
    }
  });
  
  // Memoize the enabled state to prevent unnecessary re-evaluations
  const enabled = React.useMemo(() => !!accessToken, [accessToken]);

  return useQuery<FacilityListItem[], Error>({
    queryKey: ['facilityConfiguration'], // Unique key for this query
    queryFn: fetchFacilityListItems, // The function to fetch data
    enabled: enabled, // Only run the query if accessToken exists (user is logged in)
    retry: 1, // Retry once on failure before marking as error
    staleTime: 300000, // 5 minutes (5 * 60 * 1000 ms)
    refetchInterval: false, // Disable automatic background refetching to reduce noise
    refetchIntervalInBackground: false, // Disable background refetching
    refetchOnWindowFocus: false, // Disable refetch on window focus to reduce noise
    refetchOnReconnect: true, // Keep network reconnect refetch
    // Optional: Add other React Query options like cacheTime, etc.
    // cacheTime: 10 * 60 * 1000, // 10 minutes (defaults to 5 minutes)
  });
};
