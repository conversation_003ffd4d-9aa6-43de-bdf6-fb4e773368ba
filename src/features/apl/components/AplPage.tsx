/**
 * AplPage Component
 * 
 * Main container component for the APL (Authorized Patient List) feature.
 * Provides a complete search interface with facility selection, date filtering,
 * visit ID lookup, and data grid display. Uses custom hooks for state management
 * and business logic separation.
 * 
 * Key concepts for React/TS newcomers:
 * - Custom hooks composition: Combines multiple focused hooks (useFacilityData, useFormState, useAplSearch)
 * - Business logic coordination: Handles auto-fetching and app-level state synchronization
 * - Material-UI components: Uses MUI Grid, Paper, Container for responsive layout
 * - TypeScript: Strong typing for all props and event handlers
 * 
 * Architecture: Simplified from provider pattern since this state is only used in one place
 */
import { useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Paper,
  Grid as MuiGrid,
  FormControlLabel,
  Checkbox,
  Button,
} from '@mui/material';
import FacilityStatus from './FacilityStatus';
import SmartDatePicker from '../../../shared/components/SmartDatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import VisitIdField from './VisitIdField';
import SearchResults from './SearchResults';
import { useFacilityData } from '../hooks/useFacilityData';
import { useFormState } from '../hooks/useFormState';
import { useAplSearch } from '../hooks/useAplSearch';
import { useAplPageHandlers } from '../hooks/useAplPageHandlers';
import { useFacilityActions } from '../../../shared/hooks/useFacilityInfoZustand';

/**
 * Renders the APL (Admission, Patient, Location) page, providing facility selection,
 * search filters, and results display. All logic is contained within this component,
 * without using provider/context patterns, as it is the sole consumer.
 *
 * Features:
 * - Facility selection and status display
 * - Search filters: Visit ID, date range, and "Open w/o Datalink" (disabled)
 * - Search button to fetch APL records based on selected filters
 * - Displays search results with loading, error, and refresh capabilities
 * - Synchronizes selected facility with app-level context
 * - Automatically fetches records when facility changes, preserving original auto-fetch behavior
 *
 * Hooks Used:
 * - `useFacilityData`: Manages facility selection and status
 * - `useFormState`: Manages search filter state
 * - `useAplSearch`: Handles APL record fetching and search state
 * - `useAppContext`: Syncs selected facility with global context
 * - `useAplPageHandlers`: Provides event handlers for facility change and search actions
 *
 * @returns {JSX.Element} The rendered APL page UI
 */
function AplPage() {
  // Move all the logic directly here - no need for provider/context pattern
  // since this is the only consumer
  // Use our focused hooks directly
  const facilityData = useFacilityData();
  const formState = useFormState();
  const aplSearch = useAplSearch();

  // Get app-level facility context for syncing
  const { setSelectedFacilityFull } = useFacilityActions();

  // Track facility changes to preserve original auto-fetch behavior
  const lastFetchedFacilityRef = useRef<string | null>(null);

  // Sync facility selection with app context whenever it changes
  useEffect(() => {
    if (facilityData.selectedFacilityListItem) {
      setSelectedFacilityFull(facilityData.selectedFacilityListItem);
    }
  }, [facilityData.selectedFacilityListItem, setSelectedFacilityFull]);
  // Auto-fetch when facility changes (intentionally NOT including form state to avoid triggering on form changes)
  /* eslint-disable react-hooks/exhaustive-deps */
  useEffect(() => {
    const location = window.location;
    const isOnAplRoute = location.pathname.startsWith('/apl');

    if (!isOnAplRoute ||
      !facilityData.selectedFacilityId ||
      facilityData.isLoadingFacilities ||
      facilityData.isErrorFacilities) {
      return;
    }

    if (lastFetchedFacilityRef.current !== facilityData.selectedFacilityId) {
      const filters = {
        facilityId: facilityData.selectedFacilityId,
        fromDate: formState.fromDate,
        toDate: formState.toDate,
        visitId: formState.visitId || undefined,
        mrn: formState.mrn || undefined,
      };

      aplSearch.loadAplRecords(filters);
      lastFetchedFacilityRef.current = facilityData.selectedFacilityId;
    }
  }, [
    facilityData.selectedFacilityId,
    facilityData.isLoadingFacilities,
    facilityData.isErrorFacilities,
    aplSearch
  ]);
  /* eslint-enable react-hooks/exhaustive-deps */
  // Create wrapped loadAplRecords that uses current form state
  const loadAplRecords = useCallback(async () => {
    const filters = {
      facilityId: facilityData.selectedFacilityId,
      fromDate: formState.fromDate,
      toDate: formState.toDate,
      visitId: formState.visitId || undefined,
      mrn: formState.mrn || undefined,
    };

    await aplSearch.loadAplRecords(filters);
  }, [
    facilityData.selectedFacilityId,
    formState.fromDate,
    formState.toDate,
    formState.visitId,
    formState.mrn,
    aplSearch
  ]);

  // Get event handlers from custom hook
  const { handleFacilityChange, handleSearch } = useAplPageHandlers({
    loadAplRecords,
  });
  return (
    <Box sx={{ mb: 3 }}>
      <Paper sx={{ p: 2 }}>
        <MuiGrid container spacing={2} alignItems="center" sx={{ mb: 1 }}>
          <MuiGrid item xs={12} sm={2.5}>
            <FacilityStatus
              isLoading={facilityData.isLoadingFacilities}
              isFetching={facilityData.isFetchingFacilities}
              isError={facilityData.isErrorFacilities}
              error={facilityData.facilityError}
              facilities={facilityData.facilities}
              selectedFacilityId={facilityData.selectedFacilityId}
              onFacilityChange={handleFacilityChange} />
          </MuiGrid>
          <MuiGrid item xs={12} sm={1.5}>
            <VisitIdField
              value={formState.visitId}
              onChange={formState.setVisitId} />
          </MuiGrid>
          <MuiGrid item xs={12} sm={2}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <SmartDatePicker
                label="From Date"
                value={formState.fromDate}
                onAccept={(newDate) => formState.updateDateWithParams(newDate, formState.toDate)} />
            </LocalizationProvider>
          </MuiGrid>
          <MuiGrid item xs={12} sm={2}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <SmartDatePicker
                label="To Date"
                value={formState.toDate}
                onAccept={(newDate) => formState.updateDateWithParams(formState.fromDate, newDate)} />
            </LocalizationProvider>
          </MuiGrid>
          <MuiGrid item xs={12} sm={2.5}>
            <FormControlLabel
              control={<Checkbox
                checked={formState.openWithoutDatalink}
                onChange={(e) => formState.setOpenWithoutDatalink(e.target.checked)}
                disabled />}
              label="Open w/o Datalink" />
          </MuiGrid>
          <MuiGrid item xs={12} sm={1.5}>
            <Button
              variant="contained"
              onClick={handleSearch}
              disabled={aplSearch.isLoading || !facilityData.selectedFacilityId}
              fullWidth
            >
              {aplSearch.isLoading ? 'Loading...' : 'Search'}
            </Button>
          </MuiGrid>
        </MuiGrid>

        <SearchResults
          isLoading={aplSearch.isLoading}
          isFetching={aplSearch.isFetching}
          error={aplSearch.error}
          records={aplSearch.aplRecords}
          totalRecords={aplSearch.totalRecords}
          onFilterChange={aplSearch.handleFilterChange}
          onRefresh={aplSearch.refreshAplRecords} />
      </Paper>
    </Box>
  );
}

export default AplPage;
