/**
 * FacilityStatus Component
 * 
 * Smart wrapper component that handles different states of facility data loading.
 * Provides appropriate UI feedback for loading, error, and empty states while
 * delegating the actual facility selection to the FacilitySelector component.
 * Demonstrates the adapter pattern for handling async data states.
 * 
 * Key concepts for React/TS newcomers:
 * - State management: Handling loading, error, and success states
 * - Conditional rendering: Different UI based on data loading state
 * - Component composition: Wraps FacilitySelector with status handling
 * - Error boundaries: User-friendly error display with fallback messages
 * - Loading states: Visual feedback during async operations
 * - TypeScript unions: Flexible error type handling
 */
import React from 'react';
import {
  Box,
  CircularProgress,
  Typography,
} from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import FacilitySelector from './FacilitySelector';

interface FacilityStatusProps {
  isLoading: boolean;
  isFetching?: boolean;
  isError: boolean;
  error?: Error | { message?: string } | null;
  facilities: Array<{ oid: number; longName: string }>;
  selectedFacilityId: string;
  onFacilityChange: (facilityId: string) => void;
}

const FacilityStatus: React.FC<FacilityStatusProps> = ({
  isLoading,
  isFetching = false,
  isError,
  error,
  facilities,
  selectedFacilityId,
  onFacilityChange,
}) => {
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <CircularProgress size={20} sx={{ mr: 1 }} />
        <Typography variant="body2">Loading facilities...</Typography>
      </Box>
    );
  }

  if (isError) {
    return (
      <Typography variant="body2" color="error">
        Error loading facilities: {error?.message || 'Unknown error'}
      </Typography>
    );
  }

  if (!facilities || facilities.length === 0) {
    return (
      <Typography variant="body2">No facilities available.</Typography>
    );
  }

  // Transform facility data for FacilitySelector
  const transformedFacilities = facilities.map(facility => ({
    id: facility.oid.toString(),
    name: facility.longName,
  }));

  return (
    <Box sx={{ position: 'relative', width: '100%' }}>
      <FacilitySelector
        value={selectedFacilityId}
        onChange={onFacilityChange}
        facilities={transformedFacilities}
      />
      {isFetching && (
        <Box
          sx={{
            position: 'absolute',
            top: '60%', // Adjusted for MUI FormControl with label
            right: 32, // Position to the left of the dropdown arrow
            transform: 'translateY(-50%)', // Center vertically
            zIndex: 1,
            pointerEvents: 'none', // Don't interfere with dropdown clicks
          }}
        >
          <RefreshIcon
            fontSize="small"
            sx={{
              animation: 'refreshSpin 1s linear infinite',
              color: 'primary.main',
              opacity: 0.5,
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default React.memo(FacilityStatus);