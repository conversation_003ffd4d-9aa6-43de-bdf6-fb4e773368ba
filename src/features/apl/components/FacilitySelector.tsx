/**
 * FacilitySelector Component
 * 
 * Reusable dropdown component for selecting medical facilities.
 * Provides a clean, accessible interface using Material-UI Select component
 * with proper labeling and change event handling. Designed to be composable
 * and reusable across different parts of the application.
 * 
 * Key concepts for React/TS newcomers:
 * - Controlled components: Value and onChange pattern for form state management
 * - Material-UI Select: Dropdown component with built-in accessibility features
 * - TypeScript interfaces: Clear prop contracts and event type safety
 * - Reusable components: Generic design for use in multiple contexts
 * - Event handling: Proper SelectChangeEvent typing for type safety
 */
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import type { SelectChangeEvent } from '@mui/material';

export interface Facility {
  id: string;
  name: string;
}

interface FacilitySelectorProps {
  value: string;
  onChange: (value: string) => void;
  facilities: Facility[];
}

const FacilitySelector = ({ value, onChange, facilities }: FacilitySelectorProps) => {
  
  const handleChange = (event: SelectChangeEvent) => {
    onChange(event.target.value);
  };

  return (
    <FormControl fullWidth size="small">
      <InputLabel id="facility-select-label">Facility</InputLabel>
      <Select
        labelId="facility-select-label"
        id="facility-select"
        value={value}
        label="Facility"
        onChange={handleChange}
      >
        {facilities.map((facility) => (
          <MenuItem key={facility.id} value={facility.id}>
            {facility.name}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default FacilitySelector;