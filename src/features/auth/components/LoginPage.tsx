import React from 'react';
import LoginForm from './LoginForm';

const pageStyle: React.CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '100vh',
  backgroundColor: '#f0f2f5', // A light background for the page
};

const LoginPage: React.FC = () => {
  return (
    <div style={pageStyle}>
      {/* You can add a logo or app name here if you like */}
      {/* <h1>My Application</h1> */}
      <LoginForm />
    </div>
  );
};

export default LoginPage; 