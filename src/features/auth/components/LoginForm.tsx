import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { CircularProgress, Box, Typography, TextField, Button } from '@mui/material';

// Basic styling (can be expanded or moved to a CSS file)
const styles: { [key: string]: React.CSSProperties } = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: '10px 20px',
    border: '1px solid #ccc',
    borderRadius: '8px',
    maxWidth: '300px',
    margin: '50px auto',
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  },
  error: {
    color: 'red',
    marginTop: '10px',
  },
};

const LoginForm: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const { login, accessToken, user, error, isLoading } = useAuth();
  const navigate = useNavigate();

  // This useEffect handles navigation if the component mounts and the user is already logged in.
  useEffect(() => {
    // console.log('LoginForm useEffect (mount/update check) triggered. accessToken:', accessToken, 'user:', user);
    if (accessToken && user) {
      console.log('LoginForm: Already authenticated on mount/update. Navigating to /apl/facility...');
      navigate('/apl/facility', { replace: true });
    }
  }, [accessToken, user, navigate]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    try {
      const loginResultPath = await login(username, password);
      if (loginResultPath) {
        console.log('LoginForm handleSubmit: Login successful, navigating to:', loginResultPath);
        navigate(loginResultPath, { replace: true });
      } else {
        // Error should already be set in AuthContext and will be displayed by the form
        console.log('LoginForm handleSubmit: Login reported as unsuccessful (path is null).');
      }
    } catch (err) {
      // This catch block might be redundant if login() itself doesn't throw for application errors
      // but handles them by returning false and setting error state in AuthContext.
      // However, it's good for unexpected errors during the login call itself.
      console.error('LoginForm handleSubmit: Unexpected error during login attempt:', err);
    }
  };

  // If already authenticated (e.g., on component mount), show redirecting. 
  // The useEffect above should handle the actual navigation.
  if (accessToken && user) {
    return (
      <Box sx={styles.container}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2 }}>
          <CircularProgress size={24} sx={{ mr: 1 }} />
          <Typography variant="body1">Redirecting...</Typography>
        </Box>
      </Box>
    );
  }

  // If login API call is in progress.
  if (isLoading) {
    return (
      <Box sx={styles.container}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 2 }}>
          <CircularProgress size={24} sx={{ mr: 1 }} />
          <Typography variant="body1">Logging in...</Typography>
        </Box>
      </Box>
    );
  }

  // Default: Show login form.
  return (
    <Box sx={styles.container} component="form" onSubmit={handleSubmit}>
      <Typography variant="h5" component="h2" gutterBottom>
        Temporary Login
      </Typography>
      <TextField
        label="Username"
        variant="outlined"
        value={username}
        onChange={(e) => setUsername(e.target.value)}
        margin="normal"
        required
        fullWidth
        disabled={isLoading}
        autoFocus
        autoComplete="username"
        InputLabelProps={{ shrink: true }}
      />
      <TextField
        label="Password"
        type="password"
        variant="outlined"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        margin="normal"
        required
        fullWidth
        disabled={isLoading}
        autoComplete="current-password"
        InputLabelProps={{ shrink: true }}
      />
      <Button 
        type="submit" 
        variant="contained" 
        color="primary" 
        disabled={isLoading} 
        fullWidth 
        sx={{ mt: 2, mb: 2 }}
      >
        Login
      </Button>
      {error && <Typography color="error" sx={styles.error}>{error}</Typography>}
      <Typography variant="caption" sx={{ mt: 1, color: '#777'}}>
        Warning: This is a temporary login form for development purposes only. Do not use in production.
      </Typography>
    </Box>
  );
};

export default LoginForm; 