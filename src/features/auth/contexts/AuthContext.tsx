/* eslint-disable react-refresh/only-export-components */
import React, { createContext, useContext, useState, type ReactNode } from 'react';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import { authConfig } from '../../../config/authConfig';
import { setAuthToken } from '../services/tokenManager';
import { addLogoutEventListener, removeLogoutEventListener } from '../services/authEventManager';
import { useQueryClient } from '@tanstack/react-query';

const AUTH_STORAGE_KEY = 'vic_auth_state';
const STORAGE_TYPE = sessionStorage; // or localStorage - change based on security needs

// Helper function to check if JWT token is expired
const isTokenExpired = (token: string): boolean => {
  try {
    const decoded = jwtDecode<{ exp?: number }>(token);
    if (!decoded.exp) return false; // No expiration claim, assume valid
    
    const currentTime = Date.now() / 1000; // Convert to seconds
    return decoded.exp < currentTime;
  } catch (error) {
    console.error('Failed to decode token for expiration check:', error);
    return true; // Assume expired if we can't decode
  }
};

interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in?: number;
  token_type?: string;
}

// Define a more specific User interface
interface User {
  username: string; // The username used for login
  displayName?: string; // The display name from the token
  // Add other user-related fields from the token if needed
  // e.g., sub?: string; email?: string;
}

// Define the structure of the decoded JWT payload
interface DecodedToken {
  sub?: string; // Subject (often user ID)
  'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'?: string;
  name?: string; // Sometimes a simpler 'name' claim is also present
  // Add other claims you expect or need from your token
  [key: string]: unknown; // To allow for other custom claims
}

interface AuthState {
  accessToken: string | null;
  user: User | null; // Use the User interface
  error: string | null;
  isLoading: boolean;
}

interface AuthContextType extends AuthState {
  login: (username: string, password: string) => Promise<string | null>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const queryClient = useQueryClient(); // Get the React Query client
  
  // Initialize state from localStorage if available
  const getInitialAuthState = (): AuthState => {
    try {
      const storedAuth = STORAGE_TYPE.getItem(AUTH_STORAGE_KEY);
      if (storedAuth) {
        const parsed = JSON.parse(storedAuth);
        // Validate that the stored data has the expected shape and token is not expired
        if (parsed.accessToken && parsed.user && !isTokenExpired(parsed.accessToken)) {
          console.log('Restored valid auth state from storage');
          return {
            accessToken: parsed.accessToken,
            user: parsed.user,
            error: null,
            isLoading: false,
          };
        } else if (parsed.accessToken && isTokenExpired(parsed.accessToken)) {
          console.log('Stored token is expired, clearing storage');
          STORAGE_TYPE.removeItem(AUTH_STORAGE_KEY);
        }
      }
    } catch (error) {
      console.error('Failed to restore auth state from storage:', error);
      STORAGE_TYPE.removeItem(AUTH_STORAGE_KEY);
    }
    
    return {
      accessToken: null,
      user: null,
      error: null,
      isLoading: false,
    };
  };

  const [authState, setAuthState] = useState<AuthState>(() => {
    const initialState = getInitialAuthState();
    // Set the token in tokenManager if we restored from localStorage
    if (initialState.accessToken) {
      setAuthToken(initialState.accessToken);
    }
    return initialState;
  });

  // Persist auth state to storage whenever it changes
  React.useEffect(() => {
    if (authState.accessToken && authState.user) {
      // Store auth state
      STORAGE_TYPE.setItem(AUTH_STORAGE_KEY, JSON.stringify({
        accessToken: authState.accessToken,
        user: authState.user,
      }));
    } else {
      // Clear auth state
      STORAGE_TYPE.removeItem(AUTH_STORAGE_KEY);
    }
  }, [authState.accessToken, authState.user]);

  // Clear any stale intended paths on initial mount
  React.useEffect(() => {
    // Only clear if user is not authenticated on mount
    if (!authState.accessToken) {
      STORAGE_TYPE.removeItem('intendedPath');
    }
  }, []); // Empty dependency array means this runs once on mount

  // Effect to update tokenManager when accessToken changes in state
  React.useEffect(() => {
    console.log('AuthContext: useEffect triggered. authState.accessToken:', authState.accessToken); // DEBUG LOG
    setAuthToken(authState.accessToken);
  }, [authState.accessToken]);

  const internalLogout = React.useCallback(() => {
    setAuthState({
      accessToken: null,
      user: null,
      error: null,
      isLoading: false,
    });
    // Clear any stored intended paths to prevent pre-login navigation issues
    STORAGE_TYPE.removeItem('intendedPath');
    // Clear React Query cache to prevent cached data from persisting across sessions
    queryClient.clear();
    console.log('Logged out via AuthContext due to event or direct call.');
    // Force redirect to login page after logout to ensure clean state
    window.location.href = '/login';
  }, [queryClient]);

  // Effect to listen for logout events from authEventManager
  React.useEffect(() => {
    const handleLogoutEvent = () => {
      console.log('AuthContext: Received logout event.');
      internalLogout();
    };

    addLogoutEventListener(handleLogoutEvent);
    return () => {
      removeLogoutEventListener(handleLogoutEvent);
    };
  }, [internalLogout]);

  const login = async (usernameInput: string, password: string): Promise<string | null> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    const params = new URLSearchParams();
    params.append('grant_type', 'password');
    params.append('username', usernameInput);
    params.append('password', password);
    params.append('client_id', authConfig.clientId || '');
    params.append('client_secret', 'secret');
    params.append('scope', authConfig.scope || '');

    try {
      const response = await axios.post<TokenResponse>(
        authConfig.tokenEndpoint || '',
        params,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const { access_token } = response.data;
      let displayNameFromToken: string | undefined = undefined;

      if (access_token) {
        try {
          const decodedToken = jwtDecode<DecodedToken>(access_token);
          displayNameFromToken = decodedToken['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name'] || decodedToken.name;
        } catch (decodeError) {
          console.error('Failed to decode token:', decodeError);
          // Proceed without display name if decoding fails
        }

        const currentUser: User = {
          username: usernameInput, // Username used for login
          displayName: displayNameFromToken || usernameInput, // Fallback to username if display name not found
        };

        setAuthState({
          accessToken: access_token,
          user: currentUser,
          error: null,
          isLoading: false,
        });
        console.log('Temporary Login Successful. User:', currentUser, 'Token:', access_token);
        
        const intendedPath = STORAGE_TYPE.getItem('intendedPath');
        if (intendedPath) {
          STORAGE_TYPE.removeItem('intendedPath');
          console.log('[AuthContext] Login successful, redirecting to intended path:', intendedPath);
          return intendedPath;
        }
        console.log('[AuthContext] Login successful, redirecting to default path.');
        return '/apl/facility'; // Default path after successful login
      } else {
        // Handle case where access_token is not in the response
        console.error('Login response did not include access_token.');
        setAuthState(prev => ({
          ...prev,
          error: 'Login failed: No access token received.',
          isLoading: false,
        }));
        return null; // Return null if no token
      }
    } catch (err: unknown) {
      console.error('Login failed in AuthContext:', err);
      let errorMessage = 'Login failed. Please check credentials or console.';
      if (axios.isAxiosError(err) && err.response) {
        errorMessage = `Login failed: ${err.response.data?.error_description || err.response.data?.error || err.message}`;
      }
      setAuthState({
        accessToken: null,
        user: null,
        error: errorMessage,
        isLoading: false,
      });
      return null; // Return null on error
    }
  };

  const logout = () => {
    internalLogout(); // Call the internal logout logic
  };

  return (
    <AuthContext.Provider value={{ ...authState, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
