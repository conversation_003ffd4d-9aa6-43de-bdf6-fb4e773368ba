import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  storeIntendedPath, 
  shouldStoreIntendedPath, 
  getFullPath,
  devLog
} from '../../../utils/navigation';

export interface AuthGuardResult {
  isAuthenticated: boolean;
  isLoading: boolean;
  shouldRedirectToLogin: boolean;
}

/**
 * Custom hook for authentication guard logic
 * Handles storing intended paths and determining authentication state
 */
export const useAuthGuard = (): AuthGuardResult => {
  const { accessToken } = useAuth();
  const location = useLocation();

  const isAuthenticated = !!accessToken;
  const isLoading = false; // Can be extended for loading states
  const shouldRedirectToLogin = !isAuthenticated;

  useEffect(() => {
    // Store intended path if user is not authenticated and trying to access protected route
    if (!isAuthenticated && shouldStoreIntendedPath(location.pathname, location.search)) {
      const fullPath = getFullPath(location.pathname, location.search);
      storeIntendedPath(fullPath);
      devLog('Storing intended path:', fullPath);
    }
  }, [isAuthenticated, location.pathname, location.search]);

  return {
    isAuthenticated,
    isLoading,
    shouldRedirectToLogin
  };
};

/**
 * Hook for handling post-authentication navigation
 */
export const useAuthNavigation = () => {
  const location = useLocation();
  
  const getCurrentPath = () => {
    return getFullPath(location.pathname, location.search);
  };

  const isCurrentPathLoginPage = () => {
    return location.pathname === '/login';
  };

  return {
    getCurrentPath,
    isCurrentPathLoginPage
  };
}; 