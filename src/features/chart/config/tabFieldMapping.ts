/**
 * Configuration for mapping form fields to their corresponding tab indexes
 * This is the single source of truth for dirty tracking to determine which tab has unsaved changes
 *
 * Tab indexes:
 * - 0: ED Tab
 * - 1: Obs Tab
 * - 2: <PERSON>ee Tab
 * - -1: Shared fields (affect all tabs)
 */
export const FIELD_TO_TAB_MAP: Record<string, number> = {
  // ED Tab (index 0) fields
  'treatmentArea': 0,
  'edChartStatus': 0,
  'note': 0,
  'levelCheckboxStates': 0,
  'mod25': 0,
  'mod59': 0,

  // Obs Tab (index 1) fields
  'obsStart': 1,
  'obsEnd': 1,
  'isObs': 1,

  // <PERSON>ee Tab (index 2) fields
  'criticalCareMins': 2,
  'traumaActivation': 2,
  'specialNoCharge': 2,

  // Shared header fields - these affect all tabs (index -1)
  'visitId': -1,
  'medicalRecordNumber': -1,
  'lastName': -1,
  'firstName': -1,
  'dob': -1,
  'age': -1,
  'edDos': -1,
  'edStart': -1,
  'edEnd': -1,
  'isEd': -1,
  'dischargeStatus': -1,
  'provider': -1,
};

/**
 * Tab index constants for better readability
 */
export const TAB_INDEXES = {
  ED: 0,
  OBS: 1,
  PROFEE: 2,
  SHARED: -1,
} as const;

/**
 * Type definitions for tab indexes
 */
export type TabIndex = typeof TAB_INDEXES[keyof typeof TAB_INDEXES];

/**
 * Helper function to get all fields for a specific tab index
 */
export const getFieldsForTabIndex = (tabIndex: number): string[] => {
  return Object.keys(FIELD_TO_TAB_MAP).filter(field => FIELD_TO_TAB_MAP[field] === tabIndex);
};

/**
 * Helper function to determine which tab index a field belongs to
 */
export const getTabIndexForField = (fieldName: string): number | undefined => {
  return FIELD_TO_TAB_MAP[fieldName];
};

/**
 * Get all field names across all tabs
 */
export const getAllFormFields = (): string[] => {
  return Object.keys(FIELD_TO_TAB_MAP);
};

/**
 * Helper functions for backward compatibility with grouped structure
 */
export const getEdFields = (): string[] => getFieldsForTabIndex(TAB_INDEXES.ED);
export const getObsFields = (): string[] => getFieldsForTabIndex(TAB_INDEXES.OBS);
export const getProfeeFields = (): string[] => getFieldsForTabIndex(TAB_INDEXES.PROFEE);
export const getSharedFields = (): string[] => getFieldsForTabIndex(TAB_INDEXES.SHARED);

/**
 * Legacy grouped structure for backward compatibility
 * @deprecated Use FIELD_TO_TAB_MAP and helper functions instead
 */
export const TAB_FIELD_MAPPING = {
  ed: getEdFields(),
  obs: getObsFields(),
  profee: getProfeeFields(),
  shared: getSharedFields(),
} as const;

/**
 * Legacy type definitions for backward compatibility
 * @deprecated Use TabIndex instead
 */
export type TabName = keyof typeof TAB_FIELD_MAPPING;

/**
 * Type definition for all possible form field names
 */
export type FormFieldName = keyof typeof FIELD_TO_TAB_MAP;

/**
 * Legacy helper function for backward compatibility
 * @deprecated Use getFieldsForTabIndex instead
 */
export const getFieldsForTab = (tabName: TabName): readonly string[] => {
  return TAB_FIELD_MAPPING[tabName];
};

/**
 * Legacy helper function for backward compatibility
 * @deprecated Use getTabIndexForField instead
 */
export const getTabForField = (fieldName: string): TabName | null => {
  const tabIndex = getTabIndexForField(fieldName);
  if (tabIndex === TAB_INDEXES.ED) return 'ed';
  if (tabIndex === TAB_INDEXES.OBS) return 'obs';
  if (tabIndex === TAB_INDEXES.PROFEE) return 'profee';
  if (tabIndex === TAB_INDEXES.SHARED) return 'shared';
  return null;
};
