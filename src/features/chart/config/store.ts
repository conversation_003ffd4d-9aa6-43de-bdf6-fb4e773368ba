/**
 * Store Configuration
 * Centralized Zustand store configurations and retry logic
 */

/**
 * Store retry and error handling configuration
 */
export const STORE_CONFIG = {
  /**
   * Retry configuration for failed requests
   */
  retry: {
    /** Maximum number of retry attempts */
    maxRetries: 3,
    /** Base delay between retries in milliseconds */
    baseDelay: 1000, // 1 second
    /** Maximum delay between retries in milliseconds */
    maxDelay: 10000, // 10 seconds
    /** Calculate exponential backoff delay */
    calculateDelay: (retryCount: number, baseDelay: number = 1000): number =>
      Math.min(baseDelay * Math.pow(2, retryCount), 10000),
  },

  /**
   * Store persistence configuration
   */
  persistence: {
    /** Enable/disable store persistence */
    enabled: false,
    /** Local storage key for persisted store data */
    key: 'chart-store-v1',
  },

  /**
   * Performance monitoring configuration
   */
  performance: {
    /** Enable performance logging */
    enableLogging: true,
    /** Log operations taking longer than this threshold (ms) */
    slowOperationThreshold: 100,
  },

  /**
   * Error classification for retry logic
   */
  errors: {
    /** Error codes that should trigger retry attempts */
    retryable: ['NETWORK_ERROR', 'TIMEOUT_ERROR', 'SERVER_ERROR'] as const,
    /** Error codes that should not trigger retries */
    nonRetryable: ['AUTH_ERROR', 'NOT_FOUND', 'VALIDATION_ERROR'] as const,
  },
} as const;

/**
 * Store state validation configuration
 */
export const VALIDATION_CONFIG = {
  /**
   * Request validation rules
   */
  request: {
    /** Facility ID validation */
    facilityId: {
      required: true,
      type: 'number' as const,
      min: 1,
      validate: (value: unknown): value is number =>
        typeof value === 'number' && value > 0,
    },
    /** Visit ID validation */
    visitId: {
      required: true,
      type: 'string' as const,
      minLength: 1,
      validate: (value: unknown): value is string =>
        typeof value === 'string' && value.trim().length > 0,
    },
  },

  /**
   * Chart data validation rules
   */
  chartData: {
    /** Required top-level properties */
    requiredProperties: ['chart'] as const,
    /** Validate chart data structure */
    validate: (data: unknown): boolean => {
      if (!data || typeof data !== 'object') return false;
      const chartData = data as Record<string, unknown>;
      return chartData.chart !== null && typeof chartData.chart === 'object';
    },
  },
} as const;

/**
 * Default store state values
 */
export const DEFAULT_STATE = {
  /** Default loading state */
  loadingState: 'idle' as const,
  /** Default loading flag */
  isLoading: false,
  /** Default chart info */
  chartInfo: null,
  /** Default error state */
  error: null,
  /** Default last request */
  lastRequest: null,
  /** Default last updated timestamp */
  lastUpdated: null,
  /** Default retry count */
  retryCount: 0,
} as const;