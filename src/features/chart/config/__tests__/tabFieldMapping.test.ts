import {
  FIELD_TO_TAB_MAP,
  TAB_INDEXES,
  getFieldsForTabIndex,
  getTabIndexForField,
  getAllFormFields,
  // Legacy imports for backward compatibility tests
  TAB_FIELD_MAPPING,
  getFieldsForTab,
  getTabForField
} from '../tabFieldMapping';

describe('tabFieldMapping', () => {
  describe('FIELD_TO_TAB_MAP (primary mapping)', () => {
    it('should have expected fields mapped to correct tab indexes', () => {
      // ED Tab fields (index 0)
      expect(FIELD_TO_TAB_MAP['treatmentArea']).toBe(TAB_INDEXES.ED);
      expect(FIELD_TO_TAB_MAP['edChartStatus']).toBe(TAB_INDEXES.ED);
      expect(FIELD_TO_TAB_MAP['note']).toBe(TAB_INDEXES.ED);
      expect(FIELD_TO_TAB_MAP['levelCheckboxStates']).toBe(TAB_INDEXES.ED);
      expect(FIELD_TO_TAB_MAP['mod25']).toBe(TAB_INDEXES.ED);
      expect(FIELD_TO_TAB_MAP['mod59']).toBe(TAB_INDEXES.ED);

      // Obs Tab fields (index 1)
      expect(FIELD_TO_TAB_MAP['obsStart']).toBe(TAB_INDEXES.OBS);
      expect(FIELD_TO_TAB_MAP['obsEnd']).toBe(TAB_INDEXES.OBS);
      expect(FIELD_TO_TAB_MAP['isObs']).toBe(TAB_INDEXES.OBS);

      // Profee Tab fields (index 2)
      expect(FIELD_TO_TAB_MAP['traumaActivation']).toBe(TAB_INDEXES.PROFEE);
      expect(FIELD_TO_TAB_MAP['criticalCareMins']).toBe(TAB_INDEXES.PROFEE);
      expect(FIELD_TO_TAB_MAP['specialNoCharge']).toBe(TAB_INDEXES.PROFEE);

      // Shared fields (index -1)
      expect(FIELD_TO_TAB_MAP['visitId']).toBe(TAB_INDEXES.SHARED);
      expect(FIELD_TO_TAB_MAP['firstName']).toBe(TAB_INDEXES.SHARED);
      expect(FIELD_TO_TAB_MAP['lastName']).toBe(TAB_INDEXES.SHARED);
      expect(FIELD_TO_TAB_MAP['provider']).toBe(TAB_INDEXES.SHARED);
    });
  });

  describe('getFieldsForTabIndex', () => {
    it('should return correct fields for each tab index', () => {
      const edFields = getFieldsForTabIndex(TAB_INDEXES.ED);
      expect(edFields).toContain('treatmentArea');
      expect(edFields).toContain('note');
      expect(edFields).toContain('levelCheckboxStates');

      const obsFields = getFieldsForTabIndex(TAB_INDEXES.OBS);
      expect(obsFields).toContain('obsStart');
      expect(obsFields).toContain('obsEnd');
      expect(obsFields).toContain('isObs');

      const profeeFields = getFieldsForTabIndex(TAB_INDEXES.PROFEE);
      expect(profeeFields).toContain('traumaActivation');
      expect(profeeFields).toContain('criticalCareMins');

      const sharedFields = getFieldsForTabIndex(TAB_INDEXES.SHARED);
      expect(sharedFields).toContain('visitId');
      expect(sharedFields).toContain('provider');
    });
  });

  describe('getTabIndexForField', () => {
    it('should return correct tab index for known fields', () => {
      expect(getTabIndexForField('treatmentArea')).toBe(TAB_INDEXES.ED);
      expect(getTabIndexForField('obsStart')).toBe(TAB_INDEXES.OBS);
      expect(getTabIndexForField('traumaActivation')).toBe(TAB_INDEXES.PROFEE);
      expect(getTabIndexForField('visitId')).toBe(TAB_INDEXES.SHARED);
    });

    it('should return undefined for unknown fields', () => {
      expect(getTabIndexForField('unknownField')).toBeUndefined();
    });
  });

  describe('getAllFormFields', () => {
    it('should return all fields from all tabs', () => {
      const allFields = getAllFormFields();

      expect(allFields).toContain('visitId'); // shared
      expect(allFields).toContain('treatmentArea'); // ed
      expect(allFields).toContain('obsStart'); // obs
      expect(allFields).toContain('traumaActivation'); // profee
    });

    it('should not have duplicate fields', () => {
      const allFields = getAllFormFields();
      const uniqueFields = [...new Set(allFields)];

      expect(allFields.length).toBe(uniqueFields.length);
    });
  });

  // Legacy backward compatibility tests
  describe('Legacy TAB_FIELD_MAPPING (backward compatibility)', () => {
    it('should have all required tab configurations', () => {
      expect(TAB_FIELD_MAPPING).toHaveProperty('shared');
      expect(TAB_FIELD_MAPPING).toHaveProperty('ed');
      expect(TAB_FIELD_MAPPING).toHaveProperty('obs');
      expect(TAB_FIELD_MAPPING).toHaveProperty('profee');
    });

    it('should have expected fields for each tab', () => {
      expect(TAB_FIELD_MAPPING.shared).toContain('visitId');
      expect(TAB_FIELD_MAPPING.shared).toContain('firstName');
      expect(TAB_FIELD_MAPPING.shared).toContain('lastName');

      expect(TAB_FIELD_MAPPING.ed).toContain('treatmentArea');
      expect(TAB_FIELD_MAPPING.ed).toContain('edChartStatus');
      expect(TAB_FIELD_MAPPING.ed).toContain('levelCheckboxStates');

      expect(TAB_FIELD_MAPPING.obs).toContain('obsStart');
      expect(TAB_FIELD_MAPPING.obs).toContain('obsEnd');

      expect(TAB_FIELD_MAPPING.profee).toContain('traumaActivation');
      expect(TAB_FIELD_MAPPING.profee).toContain('criticalCareMins');
    });
  });

  describe('Legacy getFieldsForTab (backward compatibility)', () => {
    it('should return correct fields for each tab', () => {
      const edFields = getFieldsForTab('ed');
      expect(edFields).toContain('treatmentArea');
      expect(edFields).toContain('note');

      const obsFields = getFieldsForTab('obs');
      expect(obsFields).toContain('obsStart');
      expect(obsFields).toContain('obsEnd');
    });
  });

  describe('Legacy getTabForField (backward compatibility)', () => {
    it('should return correct tab for known fields', () => {
      expect(getTabForField('treatmentArea')).toBe('ed');
      expect(getTabForField('obsStart')).toBe('obs');
      expect(getTabForField('traumaActivation')).toBe('profee');
      expect(getTabForField('visitId')).toBe('shared');
    });

    it('should return null for unknown fields', () => {
      expect(getTabForField('unknownField')).toBe(null);
    });
  });
});
