import { useQuery } from '@tanstack/react-query';
import { 
  fetchEmLevels, 
  fetchEmLevel, 
  fetchEmLevelsFromConfig, 
  fetchMultipleEmLevelsFromConfig,
  type EAndMLevel 
} from '../api/emLevelsApi';
import { CACHE_CONFIG, CACHE_KEYS, getCacheConfig } from '../../config';

// LEGACY REACT QUERY HOOKS (Direct EM Levels endpoint)

/**
 * React Query hook for fetching all E&M levels from legacy API
 */
export const useEmLevels = () => {
  console.log('useEmLevels: Hook called');
  const cacheConfig = getCacheConfig('emLevels');

  return useQuery<EAndMLevel[], Error>({
    queryKey: CACHE_KEYS.emLevels(), // Centralized cache key
    queryFn: fetchEmLevels, // The function to fetch data
    staleTime: cacheConfig.staleTime, // From centralized config
    retry: cacheConfig.retry, // From centralized config
    gcTime: cacheConfig.gcTime, // From centralized config
  });
};

/**
 * React Query hook for fetching a specific E&M level from legacy API
 */
export const useEmLevel = (levelNumber: number) => {
  console.log(`useEmLevel: Hook called for level ${levelNumber}`);
  const cacheConfig = getCacheConfig('emLevels');

  return useQuery<EAndMLevel | null, Error>({
    queryKey: CACHE_KEYS.emLevel(levelNumber), // Centralized cache key
    queryFn: () => fetchEmLevel(levelNumber),
    enabled: levelNumber >= 1 && levelNumber <= 5, // Only run if valid level
    staleTime: cacheConfig.staleTime, // From centralized config
    retry: cacheConfig.retry, // From centralized config
    gcTime: cacheConfig.gcTime, // From centralized config
  });
};

// CONFIGURATION API REACT QUERY HOOKS (New approach)

/**
 * React Query hook for fetching EM levels from configuration API
 * @param configInstance - Configuration instance ID
 * @param listName - Name of the EM level list (default: 'emergency')
 * @param options - Additional query options
 */
export const useEmLevelsFromConfig = (
  configInstance: number | null,
  listName: string = 'emergency',
  options: { enabled?: boolean } = {}
) => {
  const cacheConfig = getCacheConfig('emLevels');

  return useQuery<EAndMLevel[], Error>({
    queryKey: ['emLevelsConfig', configInstance, listName],
    queryFn: () => {
      if (!configInstance) {
        throw new Error('Config instance is required');
      }
      return fetchEmLevelsFromConfig(configInstance, listName);
    },
    enabled: Boolean(configInstance) && (options.enabled !== false),
    staleTime: cacheConfig.staleTime, // From centralized config
    retry: cacheConfig.retry, // From centralized config
    gcTime: cacheConfig.gcTime, // From centralized config
  });
};

/**
 * React Query hook for fetching multiple EM level lists from configuration API
 * @param configInstance - Configuration instance ID
 * @param listNames - Array of list names to fetch
 * @param options - Additional query options
 */
export const useMultipleEmLevelsFromConfig = (
  configInstance: number | null,
  listNames: string[],
  options: { enabled?: boolean } = {}
) => {
  const cacheConfig = getCacheConfig('emLevels');

  return useQuery<{ [listName: string]: EAndMLevel[] }, Error>({
    queryKey: ['multipleEmLevelsConfig', configInstance, listNames.sort().join(',')],
    queryFn: () => {
      if (!configInstance) {
        throw new Error('Config instance is required');
      }
      return fetchMultipleEmLevelsFromConfig(configInstance, listNames);
    },
    enabled: Boolean(configInstance) && listNames.length > 0 && (options.enabled !== false),
    staleTime: cacheConfig.staleTime, // From centralized config
    retry: cacheConfig.retry, // From centralized config
    gcTime: cacheConfig.gcTime, // From centralized config
  });
};