/**
 * LEGACY COMPATIBILITY LAYER
 * 
 * This file provides backward compatibility for existing imports.
 * All functionality has been moved to organized subdirectories:
 * - API functions: ./api/emLevelsApi.ts
 * - React Query hooks: ./query/emLevelsQueries.ts
 * 
 * For new code, import directly from the organized structure:
 * import { fetchEmLevelsFromConfig } from '../services/api/emLevelsApi';
 * import { useEmLevelsFromConfig } from '../services/query/emLevelsQueries';
 */

// Re-export all functions from the new organized structure
export * from './api/emLevelsApi';
export * from './query/emLevelsQueries';

// Import named exports from API module
import * as emLevelsApi from './api/emLevelsApi';

// Keep the original default export structure for any existing code that depends on it
import { 
  fetchEmLevels, 
  fetchEmLevel, 
  fetchEmLevelsFromConfig,
  fetchMultipleEmLevelsFromConfig
} from './api/emLevelsApi';

import {
  useEmLevels, 
  useEmLevel,
  useEmLevelsFromConfig,
  useMultipleEmLevelsFromConfig
} from './query/emLevelsQueries';

// Export the service module for default import compatibility  
export const emLevelsService = {
  // Existing API functions (backward compatibility)
  fetchEmLevels, 
  fetchEmLevel, 
  useEmLevels, 
  useEmLevel,
  // New configuration API functions
  fetchEmLevelsFromConfig,
  fetchMultipleEmLevelsFromConfig,
  useEmLevelsFromConfig,
  useMultipleEmLevelsFromConfig
};

export default emLevelsService;
