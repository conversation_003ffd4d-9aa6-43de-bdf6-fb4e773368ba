/**
 * Store type definitions for the chart feature
 * Provides enhanced type safety and structure for Zustand stores
 */

import type { BOChartInfo } from '../../../types/chartTypes';

/**
 * Structured error information for chart operations
 * Provides detailed context for error handling and user feedback
 */
export interface ChartError {
  /** Error classification code for programmatic handling */
  code: string;
  /** Human-readable error message */
  message: string;
  /** When the error occurred */
  timestamp: Date;
  /** Additional context about the error */
  context?: {
    /** Facility ID associated with the error */
    facilityId?: number;
    /** Visit ID associated with the error */
    visitId?: string;
    /** Current retry attempt number */
    retryCount?: number;
  };
}

/**
 * Granular loading states for better UX
 * More specific than simple boolean loading flag
 */
export type LoadingState = 
  /** No operation in progress, no data loaded */
  | 'idle' 
  /** Currently fetching data */
  | 'loading' 
  /** Data successfully loaded */
  | 'success' 
  /** Error occurred during loading */
  | 'error';

/**
 * Chart store state interface
 * Contains all state properties for chart data management
 */
export interface ChartState {
  /** Chart data from API, null if not loaded */
  chartInfo: BOChartInfo | null;
  
  /** Granular loading state for precise UI control */
  loadingState: LoadingState;
  /** Legacy boolean loading flag for backward compatibility */
  isLoading: boolean;
  /** Current error state, null if no error */
  error: ChartError | null;
  
  /** Details of the most recent API request */
  lastRequest: {
    /** Facility ID used in last request */
    facilityId?: number;
    /** Visit ID used in last request */
    visitId?: string;
    /** When the request was initiated */
    timestamp?: Date;
  } | null;
  
  /** When chart data was last successfully fetched */
  lastUpdated: Date | null;
  /** Number of retry attempts for current request */
  retryCount: number;
}

/**
 * Chart store actions interface
 * Defines all available operations for chart data management
 */
export interface ChartActions {
  /**
   * Load chart data for a specific facility and visit
   * @param facilityId - The facility identifier
   * @param visitId - The visit identifier
   * @returns Promise that resolves when loading completes
   */
  loadChartData: (facilityId: number, visitId: string) => Promise<void>;
  
  /**
   * Retry the last failed request with exponential backoff
   * @returns Promise that resolves when retry completes
   */
  retryLastRequest: () => Promise<void>;
  
  /**
   * Clear chart data and reset loading state to idle
   */
  clearChartData: () => void;
  
  /**
   * Clear error state without affecting chart data
   */
  clearError: () => void;
  
  /**
   * Reset entire store to initial state
   */
  resetStore: () => void;
  
  /**
   * Manually set error state (for testing or error injection)
   * @param error - The error to set
   */
  setError: (error: ChartError) => void;
  
  /**
   * Increment retry counter (internal use)
   */
  incrementRetry: () => void;
}

/**
 * Complete chart store type combining state and actions
 * This is the main interface used by components
 */
export type ChartStore = ChartState & ChartActions;

/**
 * Selector function signatures for computed values
 * Selectors provide optimized access to derived state
 */
export interface ChartSelectors {
  /** Check if chart data is loaded and available */
  hasChartData: (state: ChartStore) => boolean;
  /** Check if this is the first load (no data and no error) */
  isFirstLoad: (state: ChartStore) => boolean;
  /** Check if retry is possible based on error type and retry count */
  canRetry: (state: ChartStore) => boolean;
  /** Extract chart identifier for cache keys */
  getChartId: (state: ChartStore) => string | null;
  /** Extract patient demographic information */
  getPatientInfo: (state: ChartStore) => {
    firstName?: string;
    lastName?: string;
    mrn?: string;
  } | null;
}

/**
 * Store configuration interface
 * Defines runtime behavior and retry logic
 */
export interface StoreConfig {
  /** Maximum number of retry attempts before giving up */
  maxRetries: number;
  /** Base delay between retries in milliseconds */
  retryDelay: number;
  /** Whether to persist store state in localStorage */
  enablePersistence: boolean;
  /** Key used for localStorage persistence */
  persistenceKey: string;
}