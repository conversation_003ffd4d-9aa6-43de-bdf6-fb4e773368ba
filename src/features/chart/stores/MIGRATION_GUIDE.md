# Chart Store Migration Guide

## Overview

This guide helps you migrate from the legacy `chartStore` to the enhanced `chartStoreEnhanced`, which provides improved error handling, retry logic, and better type safety.

## Why Migrate?

### Legacy Store Limitations
- Basic error handling (generic Error objects)
- No retry logic for failed requests
- Limited loading states (boolean only)
- No request validation
- No performance monitoring

### Enhanced Store Benefits
- **Structured Error Handling**: Typed error objects with contextual information
- **Automatic Retry Logic**: Configurable retry with exponential backoff
- **Granular Loading States**: Track exact state of requests
- **Request Validation**: Validate inputs before making API calls
- **Performance Monitoring**: Built-in timing and logging
- **Request Deduplication**: Prevent duplicate API calls
- **Computed Selectors**: Efficient derived state calculations

## Migration Steps

### Step 1: Update Imports

```typescript
// Before
import useChartStore from '@/features/chart/stores/chartStore';

// After
import useChartStoreEnhanced from '@/features/chart/stores/chartStoreEnhanced';
```

### Step 2: Update Component Usage

#### Basic Usage (Compatible)

The enhanced store maintains backward compatibility for basic usage:

```typescript
// This code works with both stores
function ChartComponent() {
  const { chartInfo, isLoading, error, loadChartData } = useChartStoreEnhanced();
  
  useEffect(() => {
    loadChartData(facilityId, visitId);
  }, [facilityId, visitId]);
  
  if (isLoading) return <Loading />;
  if (error) return <Error message={error.message} />;
  return <Chart data={chartInfo} />;
}
```

#### Enhanced Usage (New Features)

Take advantage of new features:

```typescript
import useChartStoreEnhanced from '@/features/chart/stores/chartStoreEnhanced';
import { selectCanRetry, selectLoadingCompat } from '@/features/chart/stores/selectors';

function EnhancedChartComponent() {
  const store = useChartStoreEnhanced();
  const canRetry = selectCanRetry(store);
  
  // Granular loading states
  const renderLoadingState = () => {
    switch (store.loadingState) {
      case 'idle':
        return <EmptyState />;
      case 'loading':
        return <LoadingSpinner />;
      case 'error':
        return <ErrorDisplay error={store.error} canRetry={canRetry} />;
      case 'success':
        return <ChartDisplay data={store.chartInfo} />;
    }
  };
  
  // Retry failed requests
  const handleRetry = () => {
    if (canRetry) {
      store.retryLastRequest();
    }
  };
  
  // Structured error handling
  const renderError = () => {
    if (!store.error) return null;
    
    switch (store.error.code) {
      case 'AUTH_ERROR':
        return <LoginPrompt />;
      case 'NOT_FOUND':
        return <NotFoundMessage />;
      case 'NETWORK_ERROR':
      case 'TIMEOUT_ERROR':
        return <RetryableError onRetry={handleRetry} />;
      default:
        return <GenericError />;
    }
  };
  
  return (
    <div>
      {renderLoadingState()}
      <MetaInfo 
        lastUpdated={store.lastUpdated}
        retryCount={store.retryCount}
      />
    </div>
  );
}
```

### Step 3: Update Error Handling

#### Before (Legacy)
```typescript
if (error) {
  console.error('Chart error:', error);
  return <Alert severity="error">{error.message}</Alert>;
}
```

#### After (Enhanced)
```typescript
if (error) {
  // Access structured error information
  console.error('Chart error:', {
    code: error.code,
    message: error.message,
    context: error.context,
    timestamp: error.timestamp
  });
  
  // Handle specific error types
  if (error.code === 'AUTH_ERROR') {
    return <AuthenticationRequired />;
  }
  
  if (isRetryableError(error) && selectCanRetry(store)) {
    return (
      <Alert 
        severity="error" 
        action={<Button onClick={() => store.retryLastRequest()}>Retry</Button>}
      >
        {error.message}
      </Alert>
    );
  }
  
  return <Alert severity="error">{error.message}</Alert>;
}
```

### Step 4: Use Selectors

The enhanced store provides selectors for computed values:

```typescript
import { 
  selectHasChartData,
  selectPatientInfo,
  selectVisitTimes,
  selectCanRetry,
  selectIsStale
} from '@/features/chart/stores/selectors';

function ChartInfo() {
  const store = useChartStoreEnhanced();
  
  const hasData = selectHasChartData(store);
  const patientInfo = selectPatientInfo(store);
  const visitTimes = selectVisitTimes(store);
  const isStale = selectIsStale(store, 300000); // 5 minutes
  
  return (
    <div>
      {hasData && (
        <>
          <PatientCard {...patientInfo} />
          <VisitTimeline {...visitTimes} />
          {isStale && <RefreshPrompt />}
        </>
      )}
    </div>
  );
}
```

### Step 5: Configure Retry Behavior

The enhanced store uses configuration from `config/store.ts`:

```typescript
// Default configuration
const STORE_CONFIG = {
  retry: {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 10000, // 10 seconds
  }
};

// Customize per component if needed
import { STORE_CONFIG } from '@/features/chart/config';

// Monitor retry attempts
useEffect(() => {
  if (store.retryCount > 0) {
    trackEvent('chart_retry', { 
      attempt: store.retryCount,
      errorCode: store.error?.code 
    });
  }
}, [store.retryCount]);
```

## API Differences

### New Properties

| Property | Type | Description |
|----------|------|-------------|
| `loadingState` | `'idle' \| 'loading' \| 'success' \| 'error'` | Granular loading state |
| `lastRequest` | `{ facilityId, visitId, timestamp }` | Last request details |
| `lastUpdated` | `Date \| null` | When data was last fetched |
| `retryCount` | `number` | Current retry attempt count |

### New Methods

| Method | Description |
|--------|-------------|
| `retryLastRequest()` | Retry the last failed request |
| `clearError()` | Clear error state without clearing data |
| `resetStore()` | Reset entire store to initial state |
| `setError(error)` | Manually set error state |

### Enhanced Error Object

```typescript
interface ChartError {
  code: 'NETWORK_ERROR' | 'AUTH_ERROR' | 'NOT_FOUND' | 'VALIDATION_ERROR' | 'SERVER_ERROR' | 'TIMEOUT_ERROR';
  message: string;
  timestamp: Date;
  context?: {
    facilityId?: number;
    visitId?: string;
    retryCount?: number;
  };
}
```

## Testing

Update your tests to handle enhanced store features:

```typescript
import { renderHook, act } from '@testing-library/react';
import useChartStoreEnhanced from '@/features/chart/stores/chartStoreEnhanced';

describe('Chart Store Tests', () => {
  beforeEach(() => {
    // Reset store state
    useChartStoreEnhanced.setState({
      chartInfo: null,
      loadingState: 'idle',
      isLoading: false,
      error: null,
      lastRequest: null,
      lastUpdated: null,
      retryCount: 0,
    });
  });
  
  it('should handle retry logic', async () => {
    const { result } = renderHook(() => useChartStoreEnhanced());
    
    // Simulate network error
    mockAxios.get.mockRejectedValueOnce(new Error('Network error'));
    
    await act(async () => {
      await result.current.loadChartData(123, 'TEST');
    });
    
    expect(result.current.error?.code).toBe('NETWORK_ERROR');
    expect(result.current.retryCount).toBe(0);
    
    // Retry request
    mockAxios.get.mockResolvedValueOnce({ data: mockChartData });
    
    await act(async () => {
      await result.current.retryLastRequest();
    });
    
    expect(result.current.error).toBeNull();
    expect(result.current.chartInfo).toEqual(mockChartData);
    expect(result.current.retryCount).toBe(1);
  });
});
```

## Rollback Plan

If you need to rollback to the legacy store:

1. The legacy store remains available at `chartStore.ts`
2. Simply revert the import statements
3. Remove any enhanced-store-specific code (retry logic, selectors, etc.)

## Common Gotchas

1. **isLoading vs loadingState**: The enhanced store provides both for compatibility. Use `isLoading` for backward compatibility, `loadingState` for granular control.

2. **Error Type**: Enhanced store errors are structured objects, not Error instances. Update error handling accordingly.

3. **Async Behavior**: Both stores use async methods. Always handle promises appropriately.

4. **Selector Usage**: Selectors are pure functions and safe to use in render. They're optimized for performance.

## Support

For questions or issues with the migration:
1. Check the test files for usage examples
2. Review the TypeScript types for API details
3. Contact the team for complex migration scenarios