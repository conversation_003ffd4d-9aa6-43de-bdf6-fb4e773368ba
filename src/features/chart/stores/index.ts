/**
 * Chart stores index file
 * Provides centralized exports for all store-related functionality
 */

// Store implementations
export { default as useChartStore } from './chartStore'; // Original store
export { default as useChartStoreEnhanced } from './chartStoreEnhanced'; // Enhanced store

// Types
export type { 
  ChartState, 
  ChartActions, 
  ChartStore, 
  ChartError, 
  LoadingState,
  ChartSelectors,
  StoreConfig 
} from './types';

// Selectors
export {
  chartSelectors,
  selectHasChartData,
  selectIsFirstLoad,
  selectCanRetry,
  selectChartId,
  selectPatientInfo,
  selectIsLoading,
  selectIsSuccess,
  selectIsError,
  selectIsIdle,
  selectLoadingCompat
} from './selectors';

// Utilities
export {
  createChartError,
  errors,
  isRetryableError,
  isAuthError,
  isNotFoundError,
  validateChartRequest,
  isSameRequest,
  calculateRetryDelay,
  isValidChartState,
  createPerformanceLogger
} from './utils';

// Default export for backward compatibility
export { default } from './chartStore';