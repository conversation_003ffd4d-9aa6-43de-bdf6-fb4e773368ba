/**
 * Store selectors for the chart feature
 * Provides computed values and derived state for better encapsulation
 */

import type { ChartStore, ChartSelectors } from './types';

/**
 * Chart Store Selectors
 * These provide computed values and derived state from the store
 */
export const chartSelectors: ChartSelectors = {
  /**
   * Check if chart data is available
   */
  hasChartData: (state: ChartStore): boolean => {
    return state.chartInfo !== null;
  },

  /**
   * Check if this is the first load (no previous data)
   */
  isFirstLoad: (state: ChartStore): boolean => {
    return state.chartInfo === null && state.lastRequest === null;
  },

  /**
   * Check if retry is available (has error and hasn't exceeded max retries)
   */
  canRetry: (state: ChartStore): boolean => {
    return state.error !== null && state.retryCount < 3;
  },

  /**
   * Get chart identifier for caching/comparison
   */
  getChartId: (state: ChartStore): string | null => {
    if (!state.chartInfo || !state.lastRequest) return null;
    return `${state.lastRequest.facilityId}-${state.lastRequest.visitId}`;
  },

  /**
   * Extract patient information for display
   */
  getPatientInfo: (state: ChartStore) => {
    if (!state.chartInfo?.chart) return null;
    
    const chart = state.chartInfo.chart;
    return {
      firstName: chart.patientFirstName || undefined,
      lastName: chart.patientLastName || undefined,
      mrn: chart.mrn || undefined,
    };
  },
};

/**
 * Individual selector functions for easy consumption
 */
export const selectHasChartData = (state: ChartStore) => chartSelectors.hasChartData(state);
export const selectIsFirstLoad = (state: ChartStore) => chartSelectors.isFirstLoad(state);
export const selectCanRetry = (state: ChartStore) => chartSelectors.canRetry(state);
export const selectChartId = (state: ChartStore) => chartSelectors.getChartId(state);
export const selectPatientInfo = (state: ChartStore) => chartSelectors.getPatientInfo(state);

/**
 * Loading state selectors
 */
export const selectIsLoading = (state: ChartStore) => state.loadingState === 'loading';
export const selectIsSuccess = (state: ChartStore) => state.loadingState === 'success';
export const selectIsError = (state: ChartStore) => state.loadingState === 'error';
export const selectIsIdle = (state: ChartStore) => state.loadingState === 'idle';

/**
 * Computed loading status for backward compatibility
 */
export const selectLoadingCompat = (state: ChartStore) => state.loadingState === 'loading';