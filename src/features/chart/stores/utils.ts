/**
 * Store utilities for the chart feature
 * Helper functions for error handling, state validation, and common operations
 */

import type { ChartError, LoadingState } from './types';
import { STORE_CONFIG, VALIDATION_CONFIG } from '../config';

/**
 * Error Creation Utilities
 */
export const createChartError = (
  code: string,
  message: string,
  context?: ChartError['context']
): ChartError => ({
  code,
  message,
  timestamp: new Date(),
  context,
});

/**
 * Common error creators
 */
export const errors = {
  networkError: (context?: ChartError['context']) =>
    createChartError('NETWORK_ERROR', 'Failed to fetch chart data due to network issues', context),
  
  authError: (context?: ChartError['context']) =>
    createChartError('AUTH_ERROR', 'Authentication required to access chart data', context),
  
  notFoundError: (context?: ChartError['context']) =>
    createChartError('NOT_FOUND', 'Chart data not found for the specified visit', context),
  
  validationError: (message: string, context?: ChartError['context']) =>
    createChartError('VALIDATION_ERROR', message, context),
  
  serverError: (context?: ChartError['context']) =>
    createChartError('SERVER_ERROR', 'Server error occurred while fetching chart data', context),
  
  timeoutError: (context?: ChartError['context']) =>
    createChartError('TIMEOUT_ERROR', 'Request timed out while fetching chart data', context),
};

/**
 * Error classification helpers
 */
export const isRetryableError = (error: ChartError): boolean => {
  return STORE_CONFIG.errors.retryable.includes(error.code as any);
};

export const isAuthError = (error: ChartError): boolean => {
  return error.code === 'AUTH_ERROR';
};

export const isNotFoundError = (error: ChartError): boolean => {
  return error.code === 'NOT_FOUND';
};

/**
 * Loading state transitions
 */
export const getNextLoadingState = (
  currentState: LoadingState,
  action: 'start' | 'success' | 'error' | 'reset'
): LoadingState => {
  switch (action) {
    case 'start':
      return 'loading';
    case 'success':
      return 'success';
    case 'error':
      return 'error';
    case 'reset':
      return 'idle';
    default:
      return currentState;
  }
};

/**
 * Request validation
 */
export const validateChartRequest = (facilityId: number, visitId: string): string[] => {
  const errors: string[] = [];
  
  // Validate facility ID using centralized config
  const facilityConfig = VALIDATION_CONFIG.request.facilityId;
  if (!VALIDATION_CONFIG.request.facilityId.validate(facilityId)) {
    if (typeof facilityId !== 'number') {
      errors.push('Facility ID must be a number');
    } else if (facilityId <= 0) {
      errors.push('Facility ID must be a positive number');
    }
  }
  
  // Validate visit ID using centralized config
  if (!VALIDATION_CONFIG.request.visitId.validate(visitId)) {
    if (typeof visitId !== 'string') {
      errors.push('Visit ID must be a string');
    } else if (!visitId || visitId.trim() === '') {
      errors.push('Visit ID is required');
    }
  }
  
  return errors;
};

/**
 * Request comparison
 */
export const isSameRequest = (
  current: { facilityId?: number; visitId?: string } | null,
  new_: { facilityId: number; visitId: string }
): boolean => {
  if (!current) return false;
  return current.facilityId === new_.facilityId && current.visitId === new_.visitId;
};

/**
 * Retry delay calculation with exponential backoff
 */
export const calculateRetryDelay = (retryCount: number, baseDelay: number = STORE_CONFIG.retry.baseDelay): number => {
  return STORE_CONFIG.retry.calculateDelay(retryCount, baseDelay);
};

/**
 * State validation helpers
 */
export const isValidChartState = (chartInfo: any): boolean => {
  return VALIDATION_CONFIG.chartData.validate(chartInfo);
};

/**
 * Data transformation utilities
 */
export const sanitizeChartData = (rawData: any): any => {
  if (!rawData) return null;
  
  // Basic sanitization - could be expanded based on needs
  return {
    ...rawData,
    // Ensure dates are properly formatted
    creationDate: rawData.creationDate ? new Date(rawData.creationDate) : null,
    modifiedDate: rawData.modifiedDate ? new Date(rawData.modifiedDate) : null,
  };
};

/**
 * Performance monitoring helpers
 */
export const createPerformanceLogger = () => {
  let startTime = 0;
  
  return {
    start: () => {
      startTime = performance.now();
    },
    end: (operationName: string) => {
      const duration = performance.now() - startTime;
      console.debug(`[Chart Store] ${operationName} took ${duration.toFixed(2)}ms`);
      return duration;
    },
  };
};