/**
 * LEGACY CHART STORE
 * 
 * This is the original chart store implementation, maintained for backward compatibility.
 * 
 * For new development, consider using the enhanced store:
 * import useChartStoreEnhanced from './chartStoreEnhanced';
 * 
 * The enhanced store provides:
 * - Better error handling with structured error types
 * - Retry logic for failed requests  
 * - Request validation
 * - Performance monitoring
 * - Computed selectors
 * - More granular loading states
 */

import { create } from 'zustand';
import axios from 'axios';
import type { BOChartInfo } from '../../../types/chartTypes';
import { storeLogger } from '../../../utils/logger';
import { fetchChartData } from '../services/api/chartApi';

interface ChartState {
  chartInfo: BOChartInfo | null;
  isLoading: boolean;
  error: Error | null;
  loadChartData: (facilityId: number, visitId: string) => Promise<void>;
  clearChartData: () => void;
}

/**
 * Zustand store for managing chart data state.
 *
 * @remarks
 * This store handles fetching chart data from the API, managing loading and error states,
 * and provides methods to clear chart data. It uses Axios for HTTP requests and logs
 * actions using `storeLogger`.
 *
 * @property {BOChartInfo | null} chartInfo - The current chart data, or null if not loaded.
 * @property {boolean} isLoading - Indicates if chart data is currently being fetched.
 * @property {Error | null} error - The error encountered during data fetching, if any.
 *
 * @method loadChartData
 * Loads chart data for a given facility and visit ID. Handles API errors and authentication issues.
 * @param {number} facilityId - The ID of the facility.
 * @param {string} visitId - The visit identifier.
 * @returns {Promise<void>}
 *
 * @method clearChartData
 * Clears the chart data, loading, and error states.
 */
const useChartStore = create<ChartState>((set) => ({
  chartInfo: null,
  isLoading: false,
  error: null,

  loadChartData: async (facilityId: number, visitId: string) => {
    set({ isLoading: true, error: null });
    try {
      const chartInfo = await fetchChartData(facilityId, visitId);
      
      if (!chartInfo) {
        throw new Error('API returned empty response');
      }

      storeLogger.debug('Successfully fetched and parsed chart data');
      set({ chartInfo: chartInfo, isLoading: false });
    } catch (error) {
      const err = error as Error;
      storeLogger.error('Error fetching chart data', err);
      
      if (axios.isAxiosError(error)) {
        storeLogger.error('Axios error details', {
          message: error.message,
          status: error.response?.status,
          responseData: error.response?.data,
        });
      }
      
      set({ error: err, isLoading: false, chartInfo: null });
    }
  },

  clearChartData: () => {
    storeLogger.debug('Clearing chart data');
    set({ chartInfo: null, isLoading: false, error: null });
  },
}));

export default useChartStore; 