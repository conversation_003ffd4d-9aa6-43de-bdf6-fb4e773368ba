/**
 * Tests for store utilities
 * Covers error handling, validation, and helper functions
 */

import { describe, it, expect } from 'vitest';
import {
  createChartError,
  errors,
  isRetryableError,
  isAuthError,
  isNotFoundError,
  validateChartRequest,
  isSameRequest,
  calculateRetryDelay,
  isValidChartState,
  getNextLoadingState,
} from '../utils';

describe('store utils', () => {
  describe('error creation', () => {
    it('should create chart error with all properties', () => {
      const context = { facilityId: 123, visitId: 'TEST' };
      const error = createChartError('TEST_ERROR', 'Test message', context);
      
      expect(error).toMatchObject({
        code: 'TEST_ERROR',
        message: 'Test message',
        timestamp: expect.any(Date),
        context,
      });
    });

    it('should create error without context', () => {
      const error = createChartError('TEST_ERROR', 'Test message');
      
      expect(error).toMatchObject({
        code: 'TEST_ERROR',
        message: 'Test message',
        timestamp: expect.any(Date),
      });
      expect(error.context).toBeUndefined();
    });
  });

  describe('predefined errors', () => {
    it('should create network error', () => {
      const error = errors.networkError({ facilityId: 123 });
      expect(error.code).toBe('NETWORK_ERROR');
      expect(error.message).toContain('network');
    });

    it('should create auth error', () => {
      const error = errors.authError();
      expect(error.code).toBe('AUTH_ERROR');
      expect(error.message).toContain('Authentication');
    });

    it('should create not found error', () => {
      const error = errors.notFoundError();
      expect(error.code).toBe('NOT_FOUND');
      expect(error.message).toContain('not found');
    });

    it('should create validation error with custom message', () => {
      const error = errors.validationError('Custom validation message');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.message).toBe('Custom validation message');
    });
  });

  describe('error classification', () => {
    it('should identify retryable errors', () => {
      expect(isRetryableError(errors.networkError())).toBe(true);
      expect(isRetryableError(errors.serverError())).toBe(true);
      expect(isRetryableError(errors.timeoutError())).toBe(true);
      
      expect(isRetryableError(errors.authError())).toBe(false);
      expect(isRetryableError(errors.notFoundError())).toBe(false);
      expect(isRetryableError(errors.validationError('test'))).toBe(false);
    });

    it('should identify auth errors', () => {
      expect(isAuthError(errors.authError())).toBe(true);
      expect(isAuthError(errors.networkError())).toBe(false);
    });

    it('should identify not found errors', () => {
      expect(isNotFoundError(errors.notFoundError())).toBe(true);
      expect(isNotFoundError(errors.networkError())).toBe(false);
    });
  });

  describe('loading state transitions', () => {
    it('should transition to loading when starting', () => {
      expect(getNextLoadingState('idle', 'start')).toBe('loading');
      expect(getNextLoadingState('error', 'start')).toBe('loading');
    });

    it('should transition to success when successful', () => {
      expect(getNextLoadingState('loading', 'success')).toBe('success');
    });

    it('should transition to error when failed', () => {
      expect(getNextLoadingState('loading', 'error')).toBe('error');
    });

    it('should transition to idle when reset', () => {
      expect(getNextLoadingState('success', 'reset')).toBe('idle');
      expect(getNextLoadingState('error', 'reset')).toBe('idle');
    });
  });

  describe('request validation', () => {
    it('should validate valid requests', () => {
      const errors = validateChartRequest(123, 'TEST123');
      expect(errors).toHaveLength(0);
    });

    it('should detect invalid facility ID', () => {
      let errors = validateChartRequest(0, 'TEST123');
      expect(errors).toContain('Facility ID must be a positive number');
      
      errors = validateChartRequest(-1, 'TEST123');
      expect(errors).toContain('Facility ID must be a positive number');
      
      errors = validateChartRequest('123' as any, 'TEST123');
      expect(errors).toContain('Facility ID must be a number');
    });

    it('should detect invalid visit ID', () => {
      let errors = validateChartRequest(123, '');
      expect(errors).toContain('Visit ID is required');
      
      errors = validateChartRequest(123, '   ');
      expect(errors).toContain('Visit ID is required');
      
      errors = validateChartRequest(123, 123 as any);
      expect(errors).toContain('Visit ID must be a string');
    });

    it('should detect multiple validation errors', () => {
      const errors = validateChartRequest(0, '');
      expect(errors.length).toBeGreaterThan(1);
    });
  });

  describe('request comparison', () => {
    it('should identify same requests', () => {
      const current = { facilityId: 123, visitId: 'TEST123' };
      const new_ = { facilityId: 123, visitId: 'TEST123' };
      
      expect(isSameRequest(current, new_)).toBe(true);
    });

    it('should identify different requests', () => {
      const current = { facilityId: 123, visitId: 'TEST123' };
      
      expect(isSameRequest(current, { facilityId: 456, visitId: 'TEST123' })).toBe(false);
      expect(isSameRequest(current, { facilityId: 123, visitId: 'TEST456' })).toBe(false);
      expect(isSameRequest(null, { facilityId: 123, visitId: 'TEST123' })).toBe(false);
    });
  });

  describe('retry delay calculation', () => {
    it('should calculate exponential backoff', () => {
      expect(calculateRetryDelay(0, 1000)).toBe(1000);  // 1s * 2^0 = 1s
      expect(calculateRetryDelay(1, 1000)).toBe(2000);  // 1s * 2^1 = 2s
      expect(calculateRetryDelay(2, 1000)).toBe(4000);  // 1s * 2^2 = 4s
      expect(calculateRetryDelay(3, 1000)).toBe(8000);  // 1s * 2^3 = 8s
    });

    it('should cap delay at maximum', () => {
      expect(calculateRetryDelay(10, 1000)).toBe(10000); // Capped at 10s
    });

    it('should use default base delay', () => {
      expect(calculateRetryDelay(0)).toBe(1000); // Default 1s
    });
  });

  describe('state validation', () => {
    it('should validate valid chart state', () => {
      const validState = {
        chart: { /* chart data */ },
        visitID: 'TEST123',
      };
      
      expect(isValidChartState(validState)).toBe(true);
    });

    it('should invalidate invalid chart states', () => {
      expect(isValidChartState(null)).toBe(false);
      expect(isValidChartState(undefined)).toBe(false);
      expect(isValidChartState({})).toBe(false);
      expect(isValidChartState({ chart: null })).toBe(false);
      expect(isValidChartState({ chart: 'not an object' })).toBe(false);
    });
  });
});