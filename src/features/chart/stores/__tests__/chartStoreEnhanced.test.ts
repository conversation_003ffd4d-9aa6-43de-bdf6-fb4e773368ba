/**
 * Tests for the enhanced chart store
 * Covers advanced features like error handling, retry logic, and selectors
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import useChartStoreEnhanced from '../chartStoreEnhanced';
import { errors, isRetryableError } from '../utils';
import { selectHasChartData, selectCanRetry, selectPatientInfo } from '../selectors';
import { mockChartInfo } from '../../../../test-utils/mockData';

// Mock the chart API
vi.mock('../../services/api/chartApi', () => ({
  fetchChartData: vi.fn(),
}));

// Mock logger
vi.mock('../../../../utils/logger', () => ({
  storeLogger: {
    debug: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}));

describe('chartStoreEnhanced', () => {
  beforeEach(() => {
    // Reset store state before each test
    useChartStoreEnhanced.setState({
      chartInfo: null,
      loadingState: 'idle',
      isLoading: false,
      error: null,
      lastRequest: null,
      lastUpdated: null,
      retryCount: 0,
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useChartStoreEnhanced.getState();
      
      expect(state.chartInfo).toBeNull();
      expect(state.loadingState).toBe('idle');
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
      expect(state.lastRequest).toBeNull();
      expect(state.lastUpdated).toBeNull();
      expect(state.retryCount).toBe(0);
    });
  });

  describe('enhanced error handling', () => {
    it('should create structured errors', () => {
      const error = errors.networkError({ facilityId: 123, visitId: 'TEST' });
      
      expect(error).toMatchObject({
        code: 'NETWORK_ERROR',
        message: expect.stringContaining('network'),
        timestamp: expect.any(Date),
        context: {
          facilityId: 123,
          visitId: 'TEST',
        },
      });
    });

    it('should identify retryable errors', () => {
      expect(isRetryableError(errors.networkError())).toBe(true);
      expect(isRetryableError(errors.serverError())).toBe(true);
      expect(isRetryableError(errors.timeoutError())).toBe(true);
      expect(isRetryableError(errors.authError())).toBe(false);
      expect(isRetryableError(errors.notFoundError())).toBe(false);
    });
  });

  describe('request validation', () => {
    it('should validate invalid facility ID', async () => {
      await useChartStoreEnhanced.getState().loadChartData(0, 'TEST123');
      
      const state = useChartStoreEnhanced.getState();
      expect(state.error).toBeTruthy();
      expect(state.error?.code).toBe('VALIDATION_ERROR');
      expect(state.loadingState).toBe('error');
    });

    it('should validate invalid visit ID', async () => {
      await useChartStoreEnhanced.getState().loadChartData(123, '');
      
      const state = useChartStoreEnhanced.getState();
      expect(state.error).toBeTruthy();
      expect(state.error?.code).toBe('VALIDATION_ERROR');
      expect(state.loadingState).toBe('error');
    });
  });

  describe('loading state management', () => {
    it('should transition through loading states correctly', async () => {
      const { fetchChartData } = await import('../../services/api/chartApi');
      
      // Mock with a delayed promise to test loading state
      let resolvePromise: (value: any) => void;
      const delayedPromise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      vi.mocked(fetchChartData).mockReturnValueOnce(delayedPromise);

      const loadPromise = useChartStoreEnhanced.getState().loadChartData(123, 'TEST123');
      
      // Should be loading immediately
      expect(useChartStoreEnhanced.getState().loadingState).toBe('loading');
      expect(useChartStoreEnhanced.getState().isLoading).toBe(true);
      
      // Resolve the promise
      resolvePromise!(mockChartInfo);
      await loadPromise;
      
      // Should be success after completion
      const finalState = useChartStoreEnhanced.getState();
      expect(finalState.loadingState).toBe('success');
      expect(finalState.isLoading).toBe(false);
      expect(finalState.chartInfo).toEqual(mockChartInfo);
      expect(finalState.lastUpdated).toBeInstanceOf(Date);
    });
  });

  describe('selectors', () => {
    it('should correctly identify when chart data is available', () => {
      let state = useChartStoreEnhanced.getState();
      expect(selectHasChartData(state)).toBe(false);
      
      useChartStoreEnhanced.setState({ chartInfo: mockChartInfo });
      state = useChartStoreEnhanced.getState();
      expect(selectHasChartData(state)).toBe(true);
    });

    it('should extract patient information correctly', () => {
      useChartStoreEnhanced.setState({ chartInfo: mockChartInfo });
      const state = useChartStoreEnhanced.getState();
      const patientInfo = selectPatientInfo(state);
      
      expect(patientInfo).toMatchObject({
        firstName: expect.any(String),
        lastName: expect.any(String),
        mrn: expect.any(String),
      });
    });

    it('should determine retry availability', () => {
      // No error, can't retry
      let state = useChartStoreEnhanced.getState();
      expect(selectCanRetry(state)).toBe(false);
      
      // Retryable error, can retry
      useChartStoreEnhanced.setState({ 
        error: errors.networkError(),
        retryCount: 1
      });
      state = useChartStoreEnhanced.getState();
      expect(selectCanRetry(state)).toBe(true);
      
      // Max retries exceeded, can't retry
      useChartStoreEnhanced.setState({ retryCount: 3 });
      state = useChartStoreEnhanced.getState();
      expect(selectCanRetry(state)).toBe(false);
    });
  });

  describe('retry logic', () => {
    it('should retry failed requests', async () => {
      const { fetchChartData } = await import('../../services/api/chartApi');
      
      // First call fails, second succeeds
      vi.mocked(fetchChartData)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockChartInfo);

      // Initial request fails
      await useChartStoreEnhanced.getState().loadChartData(123, 'TEST123');
      expect(useChartStoreEnhanced.getState().error).toBeTruthy();
      
      // Retry should succeed
      await useChartStoreEnhanced.getState().retryLastRequest();
      
      const finalState = useChartStoreEnhanced.getState();
      expect(finalState.loadingState).toBe('success');
      expect(finalState.chartInfo).toEqual(mockChartInfo);
      expect(finalState.error).toBeNull();
    });

    it('should not retry non-retryable errors', async () => {
      useChartStoreEnhanced.setState({ 
        error: errors.authError(),
        lastRequest: { facilityId: 123, visitId: 'TEST123', timestamp: new Date() }
      });
      
      const { fetchChartData } = await import('../../services/api/chartApi');
      const mockFetch = vi.mocked(fetchChartData);
      
      await useChartStoreEnhanced.getState().retryLastRequest();
      
      // Should not have called fetchChartData
      expect(mockFetch).not.toHaveBeenCalled();
    });
  });

  describe('store utilities', () => {
    it('should clear chart data', () => {
      useChartStoreEnhanced.setState({ 
        chartInfo: mockChartInfo,
        loadingState: 'success',
        error: errors.networkError()
      });
      
      useChartStoreEnhanced.getState().clearChartData();
      
      const state = useChartStoreEnhanced.getState();
      expect(state.chartInfo).toBeNull();
      expect(state.loadingState).toBe('idle');
      expect(state.error).toBeNull();
    });

    it('should clear errors', () => {
      useChartStoreEnhanced.setState({ 
        error: errors.networkError(),
        loadingState: 'error'
      });
      
      useChartStoreEnhanced.getState().clearError();
      
      const state = useChartStoreEnhanced.getState();
      expect(state.error).toBeNull();
      expect(state.loadingState).toBe('idle');
    });

    it('should reset store to initial state', () => {
      useChartStoreEnhanced.setState({ 
        chartInfo: mockChartInfo,
        loadingState: 'success',
        error: errors.networkError(),
        retryCount: 2,
        lastUpdated: new Date()
      });
      
      useChartStoreEnhanced.getState().resetStore();
      
      const state = useChartStoreEnhanced.getState();
      expect(state.chartInfo).toBeNull();
      expect(state.loadingState).toBe('idle');
      expect(state.error).toBeNull();
      expect(state.retryCount).toBe(0);
      expect(state.lastRequest).toBeNull();
      expect(state.lastUpdated).toBeNull();
    });
  });

  describe('performance optimization', () => {
    it('should skip duplicate requests', async () => {
      const { fetchChartData } = await import('../../services/api/chartApi');
      vi.mocked(fetchChartData).mockResolvedValue(mockChartInfo);

      // First request
      await useChartStoreEnhanced.getState().loadChartData(123, 'TEST123');
      expect(vi.mocked(fetchChartData)).toHaveBeenCalledTimes(1);
      
      // Duplicate request should be skipped
      await useChartStoreEnhanced.getState().loadChartData(123, 'TEST123');
      expect(vi.mocked(fetchChartData)).toHaveBeenCalledTimes(1); // Still just 1 call
    });

    it('should not skip different requests', async () => {
      const { fetchChartData } = await import('../../services/api/chartApi');
      vi.mocked(fetchChartData).mockResolvedValue(mockChartInfo);

      // First request
      await useChartStoreEnhanced.getState().loadChartData(123, 'TEST123');
      expect(vi.mocked(fetchChartData)).toHaveBeenCalledTimes(1);
      
      // Different request should not be skipped
      await useChartStoreEnhanced.getState().loadChartData(456, 'TEST456');
      expect(vi.mocked(fetchChartData)).toHaveBeenCalledTimes(2);
    });
  });
});