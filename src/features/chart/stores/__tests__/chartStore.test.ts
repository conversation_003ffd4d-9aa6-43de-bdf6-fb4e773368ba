// Test file for chartStore (Zustand store)
// This tests the global state management for chart data

import { describe, it, expect, vi, beforeEach } from 'vitest'
import useChartStore from '../chartStore'
import { mockChartInfo } from '../../../../test-utils/mockData'

// Mock the axios instance with correct path
vi.mock('../../../../shared/services/axiosInstance', () => ({
  default: {
    get: vi.fn()
  }
}))

describe('chartStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useChartStore.setState({
      chartInfo: null,
      isLoading: false,
      error: null
    })
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      // Act
      const state = useChartStore.getState()
      
      // Assert
      expect(state.chartInfo).toBeNull()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(typeof state.loadChartData).toBe('function')
      expect(typeof state.clearChartData).toBe('function')
    })
  })

  describe('clearChartData', () => {
    it('should reset state to initial values', () => {
      // Arrange - Set some state
      useChartStore.setState({
        chartInfo: mockChartInfo,
        isLoading: true,
        error: new Error('Test error')
      })
      
      // Act
      useChartStore.getState().clearChartData()
      
      // Assert
      const state = useChartStore.getState()
      expect(state.chartInfo).toBeNull()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
    })
  })

  describe('loadChartData', () => {
    it('should update state on successful fetch', async () => {
      // Arrange
      const axiosInstance = await import('../../../../shared/services/axiosInstance')
      const mockGet = vi.mocked(axiosInstance.default.get)
      mockGet.mockResolvedValue({ data: mockChartInfo })
      
      // Act
      await useChartStore.getState().loadChartData(123, 'TEST456')
      
      // Assert
      const state = useChartStore.getState()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBeNull()
      expect(state.chartInfo).toEqual(mockChartInfo)
      expect(mockGet).toHaveBeenCalledWith('chart/facility/123/visitid/TEST456')
    })

    it('should handle fetch errors correctly', async () => {
      // Arrange
      const axiosInstance = await import('../../../../shared/services/axiosInstance')
      const mockGet = vi.mocked(axiosInstance.default.get)
      const testError = new Error('Network error')
      mockGet.mockRejectedValue(testError)
      
      // Act
      await useChartStore.getState().loadChartData(123, 'TEST456')
      
      // Assert
      const state = useChartStore.getState()
      expect(state.isLoading).toBe(false)
      expect(state.error).toBe(testError)
      expect(state.chartInfo).toBeNull()
    })
  })
}) 