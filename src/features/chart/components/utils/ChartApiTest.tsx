/**
 * Chart Store Test Component
 * 
 * A test component to validate the functionality of the chart store.
 * This component allows for entering a facility ID and visit ID to test
 * the data fetching capabilities of the Zustand store.
 */
import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Grid,
} from '@mui/material';
import { useFacilityInfo } from '../../../../shared/hooks/useFacilityInfoZustand';
import useChartStore from '../../stores/chartStore';

const ChartApiTest: React.FC = () => {
  const { facilityInfo } = useFacilityInfo();
  
  // Hook test state
  const [hookTestVisitId, setHookTestVisitId] = useState('');
  const [hookTestFacilityId, setHookTestFacilityId] = useState('');
  
  const { 
    chartInfo: chartData, 
    isLoading, 
    error,
    loadChartData
  } = useChartStore();

  const isFetching = isLoading;
  const isSuccess = !isLoading && !error && !!chartData;

  // Populate fields with current facility
  const populateFromCurrentFacility = () => {
    if (facilityInfo?.oid) {
      setHookTestFacilityId(facilityInfo.oid.toString());
    }
  };

  const handleRunTest = () => {
    if (!hookTestVisitId || !hookTestFacilityId) {
      alert('Please provide both Visit ID and Facility ID for the hook test.');
      return;
    }
    const facilityId = Number(hookTestFacilityId);
    const visitId = hookTestVisitId;
    
    // Trigger the data fetch
    loadChartData(facilityId, visitId);
  };

  return (
    <Box sx={{ p: 3, maxWidth: '100%', height: '100vh', overflow: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        useChartData Hook Test Suite
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        This test suite validates the chart data fetching hook.
      </Typography>

      {/* Current Facility Info */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current Facility Context
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body2">
              <strong>Selected Facility:</strong> {facilityInfo?.longName || 'None'}
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="body2">
              <strong>Facility OID:</strong> {facilityInfo?.oid || 'None'}
            </Typography>
          </Grid>
        </Grid>
        <Button 
          variant="outlined" 
          size="small" 
          onClick={populateFromCurrentFacility}
          disabled={!facilityInfo?.oid}
          sx={{ mt: 1 }}
        >
          Use Current Facility ID
        </Button>
      </Paper>

      {/* useChartData Hook Test */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>useChartData Hook Test</Typography>
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Visit ID"
              value={hookTestVisitId}
              onChange={(e) => setHookTestVisitId(e.target.value)}
              size="small"
              placeholder="e.g., 12345"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Facility ID (numeric)"
              value={hookTestFacilityId}
              onChange={(e) => setHookTestFacilityId(e.target.value)}
              size="small"
              placeholder="e.g., 456"
            />
          </Grid>
        </Grid>
        
        <Button
          variant="contained"
          onClick={handleRunTest}
          disabled={isLoading || isFetching}
        >
          Run Test
        </Button>

        {(isLoading || isFetching) && <CircularProgress size={24} sx={{ ml: 2 }} />}

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error.message}
          </Alert>
        )}

        {isSuccess && chartData && (
          <Alert severity="success" sx={{ mt: 2, maxHeight: 400, overflow: 'auto' }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              ✅ Hook returned data successfully!
            </Typography>
            <Typography variant="caption" component="pre" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
              {JSON.stringify(chartData, null, 2)}
            </Typography>
          </Alert>
        )}
      </Paper>
    </Box>
  );
};

export default ChartApiTest;
