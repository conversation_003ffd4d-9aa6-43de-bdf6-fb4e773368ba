import React from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  FormControlLabel,
  Checkbox,
  IconButton,
  TextField,
  InputAdornment,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useChartConfigInstance } from '../../hooks/data/useChartConfigInstance';
import { useEmLevelsFromConfig } from '../../services/emLevelsService';
import type { EAndMLevel, EAndMLevelInlineProps } from '../../types/emLevels';
import { eAndMLevelsData } from '../../data/emLevelsData';
// Re-export the RedisplayRecord type for components that need it
export type { RedisplayRecord } from '../types/emLevels';

// Data has been moved to ../data/emLevelsData.ts for better organization

// Helper function has been moved to ../data/emLevelsData.ts for better organization

// Utility functions have been moved to ../utils/emLevelsUtils.ts for better organization

// Inline component props interface moved to ../types/emLevels.ts

// Component that uses hard-coded data only (for testing)
const EAndMLevelInlineHardcoded: React.FC<Omit<EAndMLevelInlineProps, 'useHardcodedData'>> = ({
  selections,
  onSelectionChange,
  onClose
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  
  const handleLevelCheckboxChange = (itemId: string, checked: boolean) => {
    onSelectionChange(itemId, checked);
  };
  
  const levelsData = eAndMLevelsData;
  
  const filteredLevelsData = React.useMemo(() => {
    if (!searchQuery.trim()) return levelsData;
    
    return levelsData.map(level => ({
      ...level,
      items: level.items.filter(item => 
        item.label.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })).filter(level => level.items.length > 0);
  }, [searchQuery, levelsData]);

  return (
    <Paper 
      elevation={0} 
      sx={{ 
        mt: 2, 
        p: { xs: 1, sm: 2 },
        border: '1px solid', 
        borderColor: 'divider',
        backgroundColor: 'background.paper'
      }}
    >
      <Box sx={{ mb: 2 }}>
        <TextField
          placeholder="Search procedures..."
          variant="outlined"
          size="small"
          fullWidth
          value={searchQuery}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Box component="span" sx={{ color: 'action.active' }}>
                  🔍
                </Box>
              </InputAdornment>
            ),
            endAdornment: searchQuery ? (
              <IconButton
                size="small"
                aria-label="clear search"
                onClick={() => setSearchQuery('')}
                edge="end"
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            ) : null
          }}
        />
      </Box>
      <Box sx={{ overflowX: 'auto' }}>
        <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2, width: '100%', minWidth: 900 }}>
          {filteredLevelsData.map((level) => (
            <Paper key={level.levelTitle} elevation={1} sx={{ flex: 1, p: 2, backgroundColor: 'grey.50', minWidth: 180 }}>
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', textAlign:'center', mb: 2 }}>
                {level.levelTitle}
              </Typography>
              <FormControl component="fieldset" sx={{ width: '100%' }}>
                {level.items.map((item) => (
                  <FormControlLabel
                    key={item.id}
                    control={
                      <Checkbox
                        size="small"
                        checked={selections[item.id] || false}
                        onChange={(e) => handleLevelCheckboxChange(item.id, e.target.checked)}
                      />
                    }
                    label={<Typography variant="body2">{item.label}</Typography>}
                    sx={{
                      display: 'flex',
                      width: '100%',
                      ml: -0.5,
                      mb: 0.5,
                      alignItems: 'center',
                      borderRadius: 1,
                      transition: 'background-color 0.2s',
                      px: 1,
                      mx: -1,
                      '&:hover': {
                        backgroundColor: 'rgba(25, 118, 210, 0.08)',
                      },
                      '& .MuiFormControlLabel-label': {
                        lineHeight: 1.3,
                        mt: 0.1
                      }
                    }}
                  />
                ))}
              </FormControl>
            </Paper>
          ))}
        </Box>
      </Box>
      <Box sx={{ 
        mt: 2, 
        display: 'flex', 
        justifyContent: 'center',
        borderTop: '1px solid',
        borderColor: 'divider',
        pt: 2
      }}>
        <Button 
          onClick={onClose} 
          variant="contained" 
          color="primary"
          sx={{ minWidth: 120 }}
        >
          Hide E&M Levels
        </Button>
      </Box>
    </Paper>
  );
};

// Component that uses API data with fallback to hard-coded data
const EAndMLevelInlineWithAPI: React.FC<Omit<EAndMLevelInlineProps, 'useHardcodedData'>> = ({
  selections,
  onSelectionChange,
  onClose
}) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  
  // Get config instance from chart data (via Zustand store)
  const { configInstanceOid, hasChartData, isLoading: isChartLoading } = useChartConfigInstance();
  
  // Use the chart's config instance to fetch EM levels directly
  const { data: emLevelsFromChart, isLoading: isEmLevelsLoading, error: emLevelsError } = useEmLevelsFromConfig(
    configInstanceOid,
    'emergency'
  );
  
  // No more facility-based fallback - we only use chart config or hardcoded data
  const actualEmLevels = emLevelsFromChart;
  const actualError = emLevelsError;
  const isLoading = isChartLoading || isEmLevelsLoading;
  
  // Debug: Show what config instance we got from chart
  React.useEffect(() => {
    console.log('EAndMLevelInlineWithAPI: Chart config debug:', {
      configInstanceOid,
      hasChartData,
      isChartLoading,
      hasEmLevelsFromChart: !!emLevelsFromChart,
      usingChartConfig: !!configInstanceOid,
      willFallbackToHardcoded: !emLevelsFromChart
    });
  }, [configInstanceOid, hasChartData, isChartLoading, emLevelsFromChart]);
  
  // Note: Redisplay record processing is now handled by the parent EAndMLevelSection component
  // to prevent re-processing when this component is unmounted/remounted during expand/collapse

  const handleLevelCheckboxChange = (itemId: string, checked: boolean) => {
    onSelectionChange(itemId, checked);
  };
  
  // Use API data if available, fallback to hard-coded data
  const levelsData = React.useMemo(() => {
    if (actualEmLevels) {
      console.log('EM Levels: Using chart/API data', { itemCount: actualEmLevels.length });
      return actualEmLevels;
    }
    console.log('EM Levels: Using hardcoded fallback', { error: actualError?.message, loading: isLoading });
    return eAndMLevelsData;
  }, [actualEmLevels, actualError, isLoading]);
  
  const filteredLevelsData = React.useMemo(() => {
    if (!searchQuery.trim()) return levelsData;
    
    return levelsData.map(level => ({
      ...level,
      items: level.items.filter(item => 
        item.label.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })).filter(level => level.items.length > 0);
  }, [searchQuery, levelsData]);

  return (
    <Paper 
      elevation={0} 
      sx={{ 
        mt: 2, 
        p: { xs: 1, sm: 2 },
        border: '1px solid', 
        borderColor: 'divider',
        backgroundColor: 'background.paper'
      }}
    >
      {/* Loading State */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
          <CircularProgress size={24} sx={{ mr: 2 }} />
          <Typography variant="body2" color="text.secondary">
            Loading E&M levels...
          </Typography>
        </Box>
      )}

      {/* Error State */}
      {actualError && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Unable to load E&M levels from configuration. Using default data.
          {process.env.NODE_ENV === 'development' && (
            <Typography variant="caption" component="div" sx={{ mt: 1 }}>
              Error: {actualError.message}
            </Typography>
          )}
        </Alert>
      )}

      {/* Main Content - Only show when not loading */}
      {!isLoading && (
        <>
          <Box sx={{ mb: 2 }}>
            <TextField
              placeholder="Search procedures..."
              variant="outlined"
              size="small"
              fullWidth
              value={searchQuery}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Box component="span" sx={{ color: 'action.active' }}>
                      🔍
                    </Box>
                  </InputAdornment>
                ),
                endAdornment: searchQuery ? (
                  <IconButton
                    size="small"
                    aria-label="clear search"
                    onClick={() => setSearchQuery('')}
                    edge="end"
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                ) : null
              }}
            />
          </Box>
          <Box sx={{ overflowX: 'auto' }}>
            <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2, width: '100%', minWidth: 900 }}>
              {filteredLevelsData.map((level) => (
                <Paper key={level.levelTitle} elevation={1} sx={{ flex: 1, p: 2, backgroundColor: 'grey.50', minWidth: 180 }}>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', textAlign:'center', mb: 2 }}>
                    {level.levelTitle}
                  </Typography>
                  <FormControl component="fieldset" sx={{ width: '100%' }}>
                    {level.items.map((item) => (
                      <FormControlLabel
                        key={item.id}
                        control={
                          <Checkbox
                            size="small"
                            checked={selections[item.id] || false}
                            onChange={(e) => handleLevelCheckboxChange(item.id, e.target.checked)}
                          />
                        }
                        label={<Typography variant="body2">{item.label}</Typography>}
                        sx={{
                          display: 'flex',
                          width: '100%',
                          ml: -0.5,
                          mb: 0.5,
                          alignItems: 'center',
                          borderRadius: 1,
                          transition: 'background-color 0.2s',
                          px: 1,
                          mx: -1,
                          '&:hover': {
                            backgroundColor: 'rgba(25, 118, 210, 0.08)',
                          },
                          '& .MuiFormControlLabel-label': {
                            lineHeight: 1.3,
                            mt: 0.1
                          }
                        }}
                      />
                    ))}
                  </FormControl>
                </Paper>
              ))}
            </Box>
          </Box>
          <Box sx={{ 
            mt: 2, 
            display: 'flex', 
            justifyContent: 'center',
            borderTop: '1px solid',
            borderColor: 'divider',
            pt: 2
          }}>
            <Button 
              onClick={onClose} 
              variant="contained" 
              color="primary"
              sx={{ minWidth: 120 }}
            >
              Hide E&M Levels
            </Button>
          </Box>
        </>
      )}
    </Paper>
  );
};

// Main exported component with conditional rendering
export const EAndMLevelInline: React.FC<EAndMLevelInlineProps> = ({
  useHardcodedData = false,
  ...props
}) => {
  // Auto-detect mocking environment and use hardcoded data
  const isMockingEnabled = import.meta.env.VITE_ENABLE_MOCKING === 'true';
  const shouldUseHardcodedData = useHardcodedData || isMockingEnabled;
  
  // Debug logging to show data source selection
  React.useEffect(() => {
    if (isMockingEnabled) {
      console.log('EAndMLevelInline: Using hardcoded data (mocking enabled)', {
        VITE_ENABLE_MOCKING: import.meta.env.VITE_ENABLE_MOCKING,
        explicitHardcodedData: useHardcodedData,
        usingHardcodedData: shouldUseHardcodedData
      });
    }
  }, [isMockingEnabled, useHardcodedData, shouldUseHardcodedData]);
  
  if (shouldUseHardcodedData) {
    return <EAndMLevelInlineHardcoded {...props} />;
  }
  return <EAndMLevelInlineWithAPI {...props} />;
};
