import React, { useState, useEffect, useCallback } from 'react';
import { useParams, Navigate, useNavigate } from 'react-router-dom';
import { Box, Alert, Typography, Button, CircularProgress } from '@mui/material';
import { useFacilityInfo } from '../../../../shared/hooks/useFacilityInfoZustand';
// import { useChartVersion } from '../hooks/useChartVersion'; // Removed - empty file
import { DateOfServicePrompt } from './DateOfServicePrompt';
import LoadingPage from '../../../../shared/components/LoadingPage';
import { getVersionFromClassName } from '../../configs/versionMapping';
import { chartLogger } from '../../../../utils/logger';

// Types
interface ComponentLoadingState {
  isLoading: boolean;
  error: string | null;
  component: React.ComponentType | null;
}

/**
 * Enhanced dynamic component loader with better error handling and caching
 */
const loadChartComponent = async (ecFormClassName: string): Promise<React.ComponentType> => {
  const version = getVersionFromClassName(ecFormClassName);
  chartLogger.debug('Loading component for version', { version, ecFormClassName });
  
  try {
    const module = await import(`./versions/${version}/index.ts`);
    if (!module.default) {
      throw new Error(`No default export found for version ${version}`);
    }
    return module.default;
  } catch (error) {
    chartLogger.error('Failed to load version component', { version, error });
    
    // Fallback to v2024 if version loading fails
    try {
      const fallbackModule = await import(`./versions/v2024/index.ts`);
      if (!fallbackModule.default) {
        throw new Error('Fallback component (v2024) has no default export');
      }
      chartLogger.warn('Using fallback component v2024');
      return fallbackModule.default;
    } catch (fallbackError) {
      chartLogger.error('Fallback component loading failed', fallbackError);
      throw new Error(`Failed to load both ${version} and fallback v2024 components`);
    }
  }
};

/**
 * Custom hook for managing dynamic component loading
 */
const useChartComponentLoader = (currentVersion: string | null) => {
  const [componentState, setComponentState] = useState<ComponentLoadingState>({
    isLoading: false,
    error: null,
    component: null
  });

  const loadComponent = useCallback(async (version: string) => {
    setComponentState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const component = await loadChartComponent(version);
      setComponentState({
        isLoading: false,
        error: null,
        component
      });
      chartLogger.debug('Component loaded successfully for version', { version });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load chart component';
      chartLogger.error('Component loading failed', { errorMessage, error });
      setComponentState({
        isLoading: false,
        error: errorMessage,
        component: null
      });
    }
  }, []);

  // Load component when version changes
  useEffect(() => {
    if (currentVersion && !componentState.component) {
      loadComponent(currentVersion);
    }
  }, [currentVersion, componentState.component, loadComponent]);

  // Reset component when version changes
  useEffect(() => {
    if (currentVersion) {
      setComponentState(prev => ({ ...prev, component: null }));
    }
  }, [currentVersion]);

  const retryComponentLoad = useCallback(() => {
    if (currentVersion) {
      loadComponent(currentVersion);
    }
  }, [currentVersion, loadComponent]);

  return {
    ...componentState,
    retryComponentLoad
  };
};

/**
 * Error boundary component for chart component loading errors
 */
const ChartComponentErrorBoundary: React.FC<{
  error: string;
  onRetry: () => void;
  onGoBack: () => void;
}> = ({ error, onRetry, onGoBack }) => (
  <Box 
    display="flex" 
    justifyContent="center" 
    alignItems="center" 
    minHeight="400px"
    flexDirection="column"
    gap={2}
    p={3}
    role="alert"
    aria-live="polite"
  >
    <Alert severity="error" sx={{ maxWidth: 600 }}>
      <Typography variant="h6" gutterBottom>
        Component Loading Error
      </Typography>
      <Typography variant="body1" paragraph>
        {error}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        The chart interface could not be loaded. Please try again or contact support if the problem persists.
      </Typography>
    </Alert>
    
    <Box display="flex" gap={2}>
      <Button 
        variant="contained" 
        onClick={onRetry}
        aria-label="Retry loading chart component"
      >
        Retry
      </Button>
      <Button 
        variant="outlined" 
        onClick={onGoBack}
        aria-label="Go back to previous page"
      >
        Go Back
      </Button>
    </Box>
  </Box>
);

/**
 * Factory component that determines chart version and renders appropriate ChartPage
 * Handles both existing charts (using ecFormClassName) and new charts (using date of service)
 */
export const ChartPageFactory: React.FC = () => {
  chartLogger.debug('ChartPageFactory mounted');
  const { visitId } = useParams<{ visitId: string }>();
  const { facilityInfo } = useFacilityInfo();
  const navigate = useNavigate();
  
  const {
    currentVersion,
    isLoadingVersion,
    needsDateOfService,
    error: versionError,
    setVersionFromDateOfService,
    retryVersionDetermination,
    clearVersionError,
  } = useChartVersion();

  const {
    isLoading: isLoadingComponent,
    error: componentError,
    component: ChartComponent,
    retryComponentLoad
  } = useChartComponentLoader(currentVersion);

  // Navigation handlers
  const handleGoHome = useCallback(() => {
    navigate('/', { replace: true });
  }, [navigate]);

  const handleGoBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);


  // Early returns for invalid states
  if (!visitId) {
    return <Navigate to="/" replace />;
  }

  if (!facilityInfo) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="400px"
        flexDirection="column"
        gap={2}
        role="alert"
        aria-live="polite"
      >
        <Alert severity="warning">
          <Typography variant="body1">
            Please select a facility before accessing charts.
          </Typography>
        </Alert>
        <Button 
          variant="contained" 
          onClick={handleGoHome}
          aria-label="Go to home page to select facility"
        >
          Go to Home
        </Button>
      </Box>
    );
  }

  // Show loading while determining version
  if (isLoadingVersion) {
    return (
      <LoadingPage 
        message="Determining chart version... Please wait while we load the appropriate chart interface."
      />
    );
  }

  // Show date of service prompt for new charts
  if (needsDateOfService) {
    return (
      <DateOfServicePrompt
        open={true}
        onVersionSelected={setVersionFromDateOfService}
        onCancel={handleGoBack}
        facilityName={facilityInfo.longName}
        isLoading={isLoadingVersion}
        error={versionError}
      />
    );
  }

  // Show version error state with retry option
  if (versionError && !currentVersion) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="400px"
        flexDirection="column"
        gap={2}
        p={3}
        role="alert"
        aria-live="polite"
      >
        <Alert severity="error" sx={{ maxWidth: 600 }}>
          <Typography variant="h6" gutterBottom>
            Chart Version Error
          </Typography>
          <Typography variant="body1" paragraph>
            {versionError}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Visit ID: {visitId}
          </Typography>
        </Alert>
        
        <Box display="flex" gap={2}>
          <Button 
            variant="contained" 
            onClick={retryVersionDetermination}
            disabled={isLoadingVersion}
            startIcon={isLoadingVersion ? <CircularProgress size={16} /> : undefined}
            aria-label="Retry determining chart version"
          >
            {isLoadingVersion ? 'Retrying...' : 'Retry'}
          </Button>
          <Button 
            variant="outlined" 
            onClick={handleGoBack}
            aria-label="Go back to previous page"
          >
            Go Back
          </Button>
        </Box>
      </Box>
    );
  }

  // Show component loading error
  if (componentError) {
    return (
      <ChartComponentErrorBoundary
        error={componentError}
        onRetry={retryComponentLoad}
        onGoBack={handleGoBack}
      />
    );
  }

  // Show version info and render ChartPage
  if (currentVersion) {
    // Show loading while component is being loaded
    if (isLoadingComponent || !ChartComponent) {
      return (
        <LoadingPage 
          message={`Loading ${currentVersion} interface...`}
        />
      );
    }
    
    return (
      <>
        {/* Optional: Show version info for debugging */}
        {import.meta.env.DEV && (
          <Box 
            sx={{ 
              backgroundColor: 'info.light', 
              color: 'info.contrastText', 
              p: 1, 
              textAlign: 'center',
              fontSize: '0.75rem'
            }}
            role="banner"
            aria-label={`Development info: Chart version ${currentVersion}`}
          >
            Chart Version: {currentVersion}
            {versionError && (
              <Button 
                size="small" 
                onClick={clearVersionError}
                sx={{ ml: 1, color: 'inherit' }}
                aria-label="Clear version error"
              >
                Clear Error
              </Button>
            )}
          </Box>
        )}
        
        {/* Render the dynamically loaded ChartPage component */}
        <ChartComponent />
      </>
    );
  }

  // Fallback state - should not normally reach here
  return (
    <Box 
      display="flex" 
      justifyContent="center" 
      alignItems="center" 
      minHeight="400px"
      flexDirection="column"
      gap={2}
      role="alert"
      aria-live="polite"
    >
      <Alert severity="info">
        <Typography variant="body1">
          Initializing chart interface...
        </Typography>
      </Alert>
    </Box>
  );
};

export default ChartPageFactory;
