/**
 * ChartContainer Component
 * 
 * Purpose:
 * This component acts as a wrapper for the `ChartPage`. Its primary responsibilities are:
 * 1.  Fetching chart data from the API based on the `facilityId` and `visitId`.
 * 2.  Managing the loading and error states for the data-fetching process.
 * 3.  Subscribing to the `useChartStore` (Zustand) to get the data, loading state,
 *     and error information.
 * 4.  Rendering a `LoadingPage` while data is being fetched.
 * 5.  Rendering an error message if the data fetch fails.
 * 6.  Passing the successfully fetched chart data to the `ChartPage` component for display.
 * 7.  Clearing the chart data from the store when the component unmounts to prevent
 *     displaying stale information.
 */
import React from 'react';
import { useParams } from 'react-router-dom';
import { Container, Typography, Alert } from '@mui/material';
import useChartStore from '../../stores/chartStore';
import { useFacilityOid } from '../../../../shared/hooks/useFacilityInfoZustand';
import ChartPage from '../pages/ChartPage';
import LoadingPage from '../../../../shared/components/LoadingPage';
import { chartLogger } from '../../../../utils/logger';

/**
 * ChartContainer is a React functional component responsible for managing the lifecycle
 * and rendering of chart data based on the selected facility and visit ID from the route.
 *
 * It fetches chart data when both `facilityId` and `routeVisitId` are available, and
 * ensures cleanup by clearing chart data when the component unmounts or when the IDs change.
 *
 * The component displays a loading page while data is being fetched, an error alert if
 * fetching fails, and the chart page when data is successfully loaded.
 *
 * @returns {JSX.Element} The rendered chart page, loading indicator, or error alert depending on the data state.
 */
function ChartContainer() {
  const { visitId: routeVisitId } = useParams<{ visitId: string; }>();
  const facilityId = useFacilityOid();

  const { loadChartData, clearChartData, isLoading, error, chartInfo } = useChartStore();

  React.useEffect(() => {
    chartLogger.debug('ChartContainer useEffect triggered', { 
      facilityId, 
      routeVisitId, 
      hasSelectedFacility: !!facilityId 
    });
    
    // If we don't have a facility ID but we have a visitId, we might need to handle this case
    if (!facilityId && routeVisitId) {
      chartLogger.warn('No facility ID available but visitId present. This might indicate a direct navigation to chart without facility context.');
      // For now, we'll wait for the facility to be set by navigation from APL
      // In the future, we might want to fetch facility info from the visitId
      return;
    }
    
    // Ensure we have the necessary IDs before fetching
    if (facilityId && routeVisitId) {
      chartLogger.debug('Fetching chart data', { facilityId, routeVisitId });
      loadChartData(facilityId, routeVisitId);
    } else {
      chartLogger.debug('Waiting for required data', { 
        facilityId: !!facilityId, 
        routeVisitId: !!routeVisitId 
      });
    }

    // Cleanup function to clear data when the component unmounts
    // or when the IDs change, preventing stale data display.
    return () => {
      chartLogger.debug('Cleanup triggered - clearing chart data');
      clearChartData();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [facilityId, routeVisitId]); // Intentionally excluding store functions to prevent infinite loops

  if (isLoading) {
    return <LoadingPage />;
  }

  if (error) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="error">
          <Typography>Failed to load chart data.</Typography>
          <Typography variant="body2">{error.message}</Typography>
        </Alert>
      </Container>
    );
  }

  // Show a message if we're waiting for facility context
  if (!facilityId && routeVisitId) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="info">
          <Typography>Please navigate to a chart from the APL page to ensure proper facility context.</Typography>
        </Alert>
      </Container>
    );
  }

  // Render ChartPage only when chartInfo is available
  if (chartInfo) {
    return <ChartPage />;
  }

  // Fallback for the initial render or if data fetch hasn't started
  return <LoadingPage message="Initializing chart..." />;
}

export default ChartContainer; 