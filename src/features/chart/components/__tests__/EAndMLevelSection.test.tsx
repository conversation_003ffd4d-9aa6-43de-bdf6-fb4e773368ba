// Test file for EAndMLevelSection component
// This tests the section component behavior with hard-coded data

// Mock chart store to provide chart data
vi.mock('../../stores/chartStore', () => ({
  useChartStore: vi.fn(() => ({
    chartInfo: {
      chart: {
        configInstanceVersion: 'test-config-instance-123'
      }
    }
  }))
}));

// This tests the section component behavior with hard-coded data before API migration

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { FormProvider, useForm } from 'react-hook-form'
import EAndMLevelSection from '../sections/EAndMLevelSection'
import type { ChartFormValues } from '../../hooks/data/useChartFormData'

// Mock MUI icons
vi.mock('@mui/icons-material/ExpandMore', () => ({
  default: () => <div data-testid="expand-more-icon">ExpandMoreIcon</div>
}))

// Mock the EAndMLevelInline component to focus on EAndMLevelSection logic
vi.mock('../sections/EAndMLevelSelection', async () => {
  const actual = await vi.importActual('../sections/EAndMLevelSelection')
  return {
    ...actual,
    EAndMLevelInline: ({ selections, onSelectionChange, onClose }: any) => (
      <div data-testid="em-level-inline">
        <button onClick={() => onSelectionChange('l1_test', true)}>Test Selection</button>
        <button onClick={onClose}>Test Close</button>
        <div>Selected: {Object.keys(selections).filter(key => selections[key]).length}</div>
      </div>
    )
  }
})

// Test wrapper component that provides FormProvider
const FormWrapper = ({ 
  children, 
  defaultValues 
}: { 
  children: React.ReactNode
  defaultValues?: Partial<ChartFormValues>
}) => {
  const methods = useForm<ChartFormValues>({
    defaultValues: {
      visitId: '',
      medicalRecordNumber: '',
      lastName: '',
      firstName: '',
      dob: '',
      age: '',
      edDos: '',
      edStart: '',
      edEnd: '',
      obsStart: '',
      obsEnd: '',
      isEd: false,
      isObs: false,
      treatmentArea: '',
      edChartStatus: '',
      note: '',
      dischargeStatus: '',
      provider: '',
      traumaActivation: '',
      specialNoCharge: '',
      criticalCareMins: '',
      mod25: false,
      mod59: false,
      levelCheckboxStates: {},
      ...defaultValues
    }
  })

  return (
    <FormProvider {...methods}>
      {children}
    </FormProvider>
  )
}

describe('EAndMLevelSection - Pre-Migration Baseline', () => {
  const mockProps = {
    isExpanded: true,
    onAccordionChange: vi.fn(),
    levelCheckboxStates: {},
    handleEAndMSelectionChange: vi.fn(),
    textFieldSx: {},
    isEAndMLevelInlineOpen: false,
    handleToggleEAndMLevelInline: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Basic Rendering', () => {
    it('should render the accordion with E&M Level title', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} />
        </FormWrapper>
      )

      expect(screen.getByText('E&M Level')).toBeInTheDocument()
    })

    it('should render all form fields', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} />
        </FormWrapper>
      )

      expect(screen.getByLabelText('Discharge Status')).toBeInTheDocument()
      expect(screen.getByLabelText('Provider')).toBeInTheDocument()
      expect(screen.getByLabelText('Trauma Activation')).toBeInTheDocument()
      expect(screen.getByLabelText('Special / No Charge')).toBeInTheDocument()
      expect(screen.getByLabelText('Critical Care Mins')).toBeInTheDocument()
      expect(screen.getByLabelText('Mod 25')).toBeInTheDocument()
      expect(screen.getByLabelText('Mod 59 / XU / IV Contrast')).toBeInTheDocument()
    })

    it('should render E&M Levels toggle button', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} />
        </FormWrapper>
      )

      expect(screen.getByText('Select E&M Levels')).toBeInTheDocument()
    })

    it('should show "Hide E&M Levels" when inline is open', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} isEAndMLevelInlineOpen={true} />
        </FormWrapper>
      )

      expect(screen.getByText('Hide E&M Levels')).toBeInTheDocument()
    })
  })

  describe('Accordion Behavior', () => {
    it('should call onAccordionChange when accordion is toggled', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} />
        </FormWrapper>
      )

      const accordionHeader = screen.getByText('E&M Level').closest('div')
      expect(accordionHeader).not.toBeNull()
      
      fireEvent.click(accordionHeader!)
      expect(mockProps.onAccordionChange).toHaveBeenCalled()
    })

    it('should render expanded content when isExpanded is true', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} isExpanded={true} />
        </FormWrapper>
      )

      expect(screen.getByLabelText('Discharge Status')).toBeInTheDocument()
      expect(screen.getByText('Select E&M Levels')).toBeInTheDocument()
    })
  })

  describe('Form Field Integration', () => {
    it('should populate form fields with provided values', () => {
      const defaultValues = {
        dischargeStatus: 'Home',
        provider: 'Dr. Smith',
        traumaActivation: 'Level 1',
        specialNoCharge: 'None',
        criticalCareMins: '30',
        mod25: true,
        mod59: false
      }

      render(
        <FormWrapper defaultValues={defaultValues}>
          <EAndMLevelSection {...mockProps} />
        </FormWrapper>
      )

      expect(screen.getByDisplayValue('Home')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Dr. Smith')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Level 1')).toBeInTheDocument()
      expect(screen.getByDisplayValue('None')).toBeInTheDocument()
      expect(screen.getByDisplayValue('30')).toBeInTheDocument()
      
      const mod25Checkbox = screen.getByLabelText('Mod 25') as HTMLInputElement
      const mod59Checkbox = screen.getByLabelText('Mod 59 / XU / IV Contrast') as HTMLInputElement
      
      expect(mod25Checkbox.checked).toBe(true)
      expect(mod59Checkbox.checked).toBe(false)
    })

    it('should handle form field changes', async () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} />
        </FormWrapper>
      )

      const dischargeStatusInput = screen.getByLabelText('Discharge Status')
      fireEvent.change(dischargeStatusInput, { target: { value: 'Admitted' } })

      await waitFor(() => {
        expect(dischargeStatusInput).toHaveValue('Admitted')
      })
    })
  })

  describe('E&M Level Selection Integration', () => {
    it('should toggle inline component when button is clicked', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} />
        </FormWrapper>
      )

      const toggleButton = screen.getByText('Select E&M Levels')
      fireEvent.click(toggleButton)

      expect(mockProps.handleToggleEAndMLevelInline).toHaveBeenCalledTimes(1)
    })

    it('should show inline component when isEAndMLevelInlineOpen is true', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} isEAndMLevelInlineOpen={true} />
        </FormWrapper>
      )

      expect(screen.getByTestId('em-level-inline')).toBeInTheDocument()
    })

    it('should not show inline component when isEAndMLevelInlineOpen is false', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} isEAndMLevelInlineOpen={false} />
        </FormWrapper>
      )

      expect(screen.queryByTestId('em-level-inline')).not.toBeInTheDocument()
    })

    it('should display selection count in toggle button', () => {
      const levelCheckboxStates = {
        'l1_initial_assessment': true,
        'l2_tests_by_ed': true,
        'l3_arrival_ems': false
      }

      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={levelCheckboxStates} />
        </FormWrapper>
      )

      // Should show count of selected items (2 in this case)
      expect(screen.getByText('2')).toBeInTheDocument()
    })

    it('should not show count chip when no selections', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={{}} />
        </FormWrapper>
      )

      // Should not show any count chip
      expect(screen.queryByText('0')).not.toBeInTheDocument()
    })
  })

  describe('Selected Items Display', () => {
    it('should display selected items grouped by level', () => {
      const levelCheckboxStates = {
        'l1_initial_assessment': true,
        'l1_no_meds': true,
        'l3_arrival_ems': true,
        'l5_suicide_watch': true
      }

      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={levelCheckboxStates} />
        </FormWrapper>
      )

      expect(screen.getByText('Selected E&M Procedures:')).toBeInTheDocument()
      expect(screen.getByText('Level 1 (2 procedures)')).toBeInTheDocument()
      expect(screen.getByText('Level 3 (1 procedure)')).toBeInTheDocument()
      expect(screen.getByText('Level 5 (1 procedure)')).toBeInTheDocument()
    })

    it('should display correct item labels as chips', () => {
      const levelCheckboxStates = {
        'l1_initial_assessment': true,
        'l3_arrival_ems': true
      }

      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={levelCheckboxStates} />
        </FormWrapper>
      )

      expect(screen.getByText('Initial assessment')).toBeInTheDocument()
      expect(screen.getByText('Arrival by EMS/Ambulance')).toBeInTheDocument()
    })

    it('should handle item removal when delete is clicked', async () => {
      const levelCheckboxStates = {
        'l1_initial_assessment': true
      }

      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={levelCheckboxStates} />
        </FormWrapper>
      )

      // Find the chip with delete functionality
      const initialAssessmentChip = screen.getByText('Initial assessment').closest('div')
      const deleteButton = initialAssessmentChip?.querySelector('[data-testid="CancelIcon"], [data-testid="cancel-icon"]')
      
      if (deleteButton) {
        fireEvent.click(deleteButton)
        await waitFor(() => {
          expect(mockProps.handleEAndMSelectionChange).toHaveBeenCalledWith('l1_initial_assessment', false)
        })
      }
    })

    it('should not show selected items section when no items are selected', () => {
      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={{}} />
        </FormWrapper>
      )

      expect(screen.queryByText('Selected E&M Procedures:')).not.toBeInTheDocument()
    })
  })

  describe('Hard-coded Data Integration', () => {
    it('should use eAndMLevelsData for level grouping logic', () => {
      // This test ensures the component works with the current hard-coded data structure
      const levelCheckboxStates = {
        'l1_initial_assessment': true,
        'l5_suicide_watch': true
      }

      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={levelCheckboxStates} />
        </FormWrapper>
      )

      // Should correctly identify and group by the hard-coded level structure
      expect(screen.getByText('Level 1 (1 procedure)')).toBeInTheDocument()
      expect(screen.getByText('Level 5 (1 procedure)')).toBeInTheDocument()
      
      // Should not show levels without selections
      expect(screen.queryByText('Level 2')).not.toBeInTheDocument()
      expect(screen.queryByText('Level 3')).not.toBeInTheDocument()
      expect(screen.queryByText('Level 4')).not.toBeInTheDocument()
    })

    it('should correctly map IDs to labels using hard-coded data', () => {
      // Test that the component correctly maps item IDs to their labels from eAndMLevelsData
      const testCases = [
        { id: 'l1_initial_assessment', expectedLabel: 'Initial assessment' },
        { id: 'l2_tests_by_ed', expectedLabel: 'Tests by ED staff' },
        { id: 'l3_arrival_ems', expectedLabel: 'Arrival by EMS/Ambulance' },
        { id: 'l4_cardiac_monitoring', expectedLabel: 'Cardiac monitoring' },
        { id: 'l5_suicide_watch', expectedLabel: 'Suicide watch, 1:1, q15 min checks' }
      ]

      testCases.forEach(({ id, expectedLabel }) => {
        const levelCheckboxStates = { [id]: true }
        
        const { unmount } = render(
          <FormWrapper>
            <EAndMLevelSection {...mockProps} levelCheckboxStates={levelCheckboxStates} />
          </FormWrapper>
        )

        expect(screen.getByText(expectedLabel)).toBeInTheDocument()
        unmount()
      })
    })

    it('should handle levels displayed in reverse order (Level 5 to Level 1)', () => {
      const levelCheckboxStates = {
        'l1_initial_assessment': true,
        'l2_tests_by_ed': true,
        'l3_arrival_ems': true,
        'l4_cardiac_monitoring': true,
        'l5_suicide_watch': true
      }

      render(
        <FormWrapper>
          <EAndMLevelSection {...mockProps} levelCheckboxStates={levelCheckboxStates} />
        </FormWrapper>
      )

      const levelHeaders = screen.getAllByText(/Level [1-5] \(1 procedure\)/)
      
      // Should display in reverse order (highest level first)
      expect(levelHeaders).toHaveLength(5)
      expect(levelHeaders[0]).toHaveTextContent('Level 5')
      expect(levelHeaders[4]).toHaveTextContent('Level 1')
    })
  })

  describe('Selection Count Logic', () => {
    it('should correctly count selected items', () => {
      const testCases = [
        { states: {}, expectedCount: 0 },
        { states: { 'l1_test': true }, expectedCount: 1 },
        { states: { 'l1_test': true, 'l2_test': false }, expectedCount: 1 },
        { states: { 'l1_test': true, 'l2_test': true, 'l3_test': true }, expectedCount: 3 },
        { states: { 'l1_test': false, 'l2_test': false }, expectedCount: 0 }
      ]

      testCases.forEach(({ states, expectedCount }, index) => {
        const { unmount } = render(
          <FormWrapper key={index}>
            <EAndMLevelSection {...mockProps} levelCheckboxStates={states} />
          </FormWrapper>
        )

        if (expectedCount > 0) {
          expect(screen.getByText(expectedCount.toString())).toBeInTheDocument()
        } else {
          expect(screen.queryByText('0')).not.toBeInTheDocument()
        }
        
        unmount()
      })
    })
  })
})