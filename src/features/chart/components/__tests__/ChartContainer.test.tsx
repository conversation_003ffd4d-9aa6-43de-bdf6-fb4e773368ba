// Component tests for ChartContainer
// Tests the data fetching wrapper component including loading states, error handling, and conditional rendering

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen } from '@testing-library/react'
import { renderWithProviders } from '../../../../test-utils'
import ChartContainer from '../layout/ChartContainer'
import { mockChartInfo } from '../../../../test-utils/mockData'
import useChartStore from '../../stores/chartStore'
import { useFacilityOid } from '../../../../shared/hooks/useFacilityInfoZustand'

// Mock the chart store
vi.mock('../../stores/chartStore', () => ({
  default: vi.fn()
}))

// Mock the facility hook
vi.mock('../../../../shared/hooks/useFacilityInfoZustand', () => ({
  useFacilityOid: vi.fn()
}))

// Mock React Router params
const mockParams = { visitId: 'TEST123' }
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useParams: () => mockParams
  }
})

// Mock child components to isolate ChartContainer testing
vi.mock('../ChartPage', () => ({
  default: () => <div data-testid="chart-page">ChartPage Component</div>
}))

vi.mock('../../../../shared/components/LoadingPage', () => ({
  default: ({ message }: { message?: string }) => (
    <div data-testid="loading-page">
      Loading... {message && `(${message})`}
    </div>
  )
}))

describe('ChartContainer', () => {
  // Mock store functions
  const mockLoadChartData = vi.fn()
  const mockClearChartData = vi.fn()
  
  // Default mock store state
  const mockChartStore = {
    loadChartData: mockLoadChartData,
    clearChartData: mockClearChartData,
    isLoading: false,
    error: null,
    chartInfo: null
  }

  // Default mock facility OID
  const mockFacilityOid = 123

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useChartStore).mockReturnValue(mockChartStore)
    vi.mocked(useFacilityOid).mockReturnValue(mockFacilityOid)
  })

  describe('data fetching', () => {
    it('should fetch chart data when facility and visitId are available', () => {
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(mockLoadChartData).toHaveBeenCalledWith(123, 'TEST123')
      expect(mockLoadChartData).toHaveBeenCalledTimes(1)
    })

    it('should not fetch data when facilityId is missing', () => {
      // Arrange
      vi.mocked(useFacilityOid).mockReturnValue(null)
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(mockLoadChartData).not.toHaveBeenCalled()
    })

    it('should not fetch data when visitId is missing', () => {
      // Arrange
      mockParams.visitId = ''
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(mockLoadChartData).not.toHaveBeenCalled()
      
      // Cleanup
      mockParams.visitId = 'TEST123'
    })

    it('should clear chart data on unmount', () => {
      // Act
      const { unmount } = renderWithProviders(<ChartContainer />)
      unmount()
      
      // Assert
      expect(mockClearChartData).toHaveBeenCalled()
    })

    it('should refetch data when facilityId changes', () => {
      // Arrange
      const { rerender } = renderWithProviders(<ChartContainer />)
      expect(mockLoadChartData).toHaveBeenCalledWith(123, 'TEST123')
      
      // Act - Change facility
      vi.mocked(useFacilityOid).mockReturnValue(456)
      rerender(<ChartContainer />)
      
      // Assert
      expect(mockLoadChartData).toHaveBeenCalledWith(456, 'TEST123')
      expect(mockLoadChartData).toHaveBeenCalledTimes(2)
    })

    it('should refetch data when visitId changes', () => {
      // Arrange
      renderWithProviders(<ChartContainer />)
      expect(mockLoadChartData).toHaveBeenCalledWith(123, 'TEST123')
      const initialCallCount = mockLoadChartData.mock.calls.length
      
      // Act - Change visitId
      mockParams.visitId = 'NEW_VISIT'
      const { rerender } = renderWithProviders(<ChartContainer />)
      rerender(<ChartContainer />)
      
      // Assert
      expect(mockLoadChartData).toHaveBeenCalledWith(123, 'NEW_VISIT')
      expect(mockLoadChartData.mock.calls.length).toBeGreaterThan(initialCallCount)
      
      // Cleanup
      mockParams.visitId = 'TEST123'
    })
  })

  describe('loading states', () => {
    it('should show loading page when data is being fetched', () => {
      // Arrange
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        isLoading: true
      })
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(screen.getByTestId('loading-page')).toBeInTheDocument()
      expect(screen.queryByTestId('chart-page')).not.toBeInTheDocument()
    })

    it('should show initialization loading when no data and not loading', () => {
      // Arrange
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        isLoading: false,
        chartInfo: null
      })
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(screen.getByTestId('loading-page')).toBeInTheDocument()
      expect(screen.getByText(/Initializing chart.../)).toBeInTheDocument()
    })
  })

  describe('error handling', () => {
    it('should display error message when fetch fails', () => {
      // Arrange
      const testError = new Error('Network error occurred')
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        error: testError,
        isLoading: false
      })
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(screen.getByText('Failed to load chart data.')).toBeInTheDocument()
      expect(screen.getByText('Network error occurred')).toBeInTheDocument()
      expect(screen.queryByTestId('chart-page')).not.toBeInTheDocument()
      expect(screen.queryByTestId('loading-page')).not.toBeInTheDocument()
    })

    it('should show error with proper severity', () => {
      // Arrange
      const testError = new Error('API error')
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        error: testError,
        isLoading: false
      })
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      const errorAlert = screen.getByRole('alert')
      expect(errorAlert).toBeInTheDocument()
      expect(errorAlert).toHaveClass('MuiAlert-standardError')
    })
  })

  describe('successful data loading', () => {
    it('should render ChartPage when data is successfully loaded', () => {
      // Arrange
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        chartInfo: mockChartInfo,
        isLoading: false,
        error: null
      })
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(screen.getByTestId('chart-page')).toBeInTheDocument()
      expect(screen.queryByTestId('loading-page')).not.toBeInTheDocument()
    })

    it('should not show error or loading when data is available', () => {
      // Arrange
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        chartInfo: mockChartInfo,
        isLoading: false,
        error: null
      })
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(screen.getByTestId('chart-page')).toBeInTheDocument()
      expect(screen.queryByRole('alert')).not.toBeInTheDocument()
      expect(screen.queryByTestId('loading-page')).not.toBeInTheDocument()
    })
  })

  describe('state transitions', () => {
    it('should transition from loading to success state', () => {
      // Arrange - Start with loading
      const { rerender } = renderWithProviders(<ChartContainer />)
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        isLoading: true
      })
      rerender(<ChartContainer />)
      
      expect(screen.getByTestId('loading-page')).toBeInTheDocument()
      
      // Act - Change to success state
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        chartInfo: mockChartInfo,
        isLoading: false
      })
      rerender(<ChartContainer />)
      
      // Assert
      expect(screen.getByTestId('chart-page')).toBeInTheDocument()
      expect(screen.queryByTestId('loading-page')).not.toBeInTheDocument()
    })

    it('should transition from loading to error state', () => {
      // Arrange - Start with loading
      const { rerender } = renderWithProviders(<ChartContainer />)
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        isLoading: true
      })
      rerender(<ChartContainer />)
      
      expect(screen.getByTestId('loading-page')).toBeInTheDocument()
      
      // Act - Change to error state
      const testError = new Error('Fetch failed')
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        error: testError,
        isLoading: false
      })
      rerender(<ChartContainer />)
      
      // Assert
      expect(screen.getByText('Failed to load chart data.')).toBeInTheDocument()
      expect(screen.queryByTestId('loading-page')).not.toBeInTheDocument()
    })
  })

  describe('integration behavior', () => {
    it('should handle missing facility gracefully', () => {
      // Arrange
      vi.mocked(useFacilityOid).mockReturnValue(null)
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(mockLoadChartData).not.toHaveBeenCalled()
      expect(screen.getByRole('alert')).toBeInTheDocument()
      expect(screen.getByText(/Please navigate to a chart from the APL page to ensure proper facility context./)).toBeInTheDocument()
    })

    it('should handle undefined facility oid', () => {
      // Arrange
      vi.mocked(useFacilityOid).mockReturnValue(0) // Use 0 instead of undefined to test falsy oid
      
      // Act
      renderWithProviders(<ChartContainer />)
      
      // Assert
      expect(mockLoadChartData).not.toHaveBeenCalled()
    })
  })
}) 