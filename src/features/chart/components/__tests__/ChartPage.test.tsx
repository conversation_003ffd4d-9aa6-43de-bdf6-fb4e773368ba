// Component tests for ChartPage
// Tests the main chart presentation component including form rendering, tab switching, and hook integration

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { renderWithProviders } from '../../../../test-utils'
import ChartPage from '../pages/ChartPage'
import { mockChartInfo } from '../../../../test-utils/mockData'
import useChartStore from '../../stores/chartStore'

// Mock the chart store
vi.mock('../../stores/chartStore', () => ({
  default: vi.fn()
}))

// Mock React Router params
const mockParams = { visitId: 'TEST123' }
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useParams: () => mockParams
  }
})

describe('ChartPage', () => {
  const mockChartStore = {
    chartInfo: mockChartInfo,
    isLoading: false,
    error: null,
    fetchChartData: vi.fn(),
    clearChartData: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Setup default mock implementation
    vi.mocked(useChartStore).mockReturnValue(mockChartStore)
  })

  describe('basic rendering', () => {
    it('should render the chart page with all main sections', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - Check for main structural elements
      expect(screen.getByRole('main')).toBeInTheDocument()
      expect(screen.getByRole('tablist')).toBeInTheDocument()
      
      // Check for tab labels
      expect(screen.getByRole('tab', { name: /ed/i })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /obs/i })).toBeInTheDocument()
      expect(screen.getByRole('tab', { name: /profee/i })).toBeInTheDocument()
    }, 10000)

    it('should render with chart data populated in form fields', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - Check for form fields with data
      // Note: The exact field values depend on how our hooks transform the data
      expect(screen.getByDisplayValue(mockChartInfo.chart.patientLastName || '')).toBeInTheDocument()
      expect(screen.getByDisplayValue(mockChartInfo.chart.patientFirstName || '')).toBeInTheDocument()
      expect(screen.getByDisplayValue(mockChartInfo.chart.mrn || '')).toBeInTheDocument()
    }, 10000)

    it('should start with ED tab selected by default', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert
      const edTab = screen.getByRole('tab', { name: /ed/i })
      expect(edTab).toHaveAttribute('aria-selected', 'true')
    })
  })

  describe('tab navigation', () => {
    it('should switch to observation tab when clicked', async () => {
      // Arrange
      renderWithProviders(<ChartPage />)
      const obsTab = screen.getByRole('tab', { name: /obs/i })
      
      // Act
      fireEvent.click(obsTab)
      
      // Assert
      await waitFor(() => {
        expect(obsTab).toHaveAttribute('aria-selected', 'true')
      })
      
      // Check that observation content is displayed
      expect(screen.getByText(/Observation Details/i)).toBeInTheDocument()
    })

    it('should switch to profee tab when clicked', async () => {
      // Arrange
      renderWithProviders(<ChartPage />)
      const profeeTab = screen.getByRole('tab', { name: /profee/i })
      
      // Act
      fireEvent.click(profeeTab)
      
      // Assert
      await waitFor(() => {
        expect(profeeTab).toHaveAttribute('aria-selected', 'true')
      })
      
      // Check that profee content is displayed
      expect(screen.getByText(/Billing Codes & CPT/i)).toBeInTheDocument()
    })

    it('should maintain tab state when switching between tabs', async () => {
      // Arrange
      renderWithProviders(<ChartPage />)
      const edTab = screen.getByRole('tab', { name: /ed/i })
      const obsTab = screen.getByRole('tab', { name: /obs/i })
      
      // Act - Switch to obs then back to ed
      fireEvent.click(obsTab)
      await waitFor(() => expect(obsTab).toHaveAttribute('aria-selected', 'true'), { timeout: 3000 })
      
      fireEvent.click(edTab)
      
      // Assert
      await waitFor(() => {
        expect(edTab).toHaveAttribute('aria-selected', 'true')
        expect(obsTab).toHaveAttribute('aria-selected', 'false')
      }, { timeout: 3000 })
    }, 10000)
  })

  describe('form integration', () => {
    it('should render form fields with proper labels and values', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - Check for key form fields
      // These should be rendered by the ChartHeader component
      expect(screen.getByLabelText(/visit id/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/medical record #/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/^last$/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/^first$/i)).toBeInTheDocument()
    })

    it('should handle form field changes', async () => {
      // Arrange
      renderWithProviders(<ChartPage />)
      const noteField = screen.getByRole('textbox', { name: /note/i })
      
      // Act
      fireEvent.change(noteField, { target: { value: 'Test note content' } })
      
      // Assert
      await waitFor(() => {
        expect(noteField).toHaveValue('Test note content')
      }, { timeout: 3000 })
    }, 10000)

    it('should integrate with React Hook Form properly', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - Form provider should wrap the component
      // This is verified by the presence of form fields and their ability to accept input
      const visitIdField = screen.getByLabelText(/visit id/i)
      expect(visitIdField).toBeInTheDocument()
      expect(visitIdField.closest('div')).toBeInTheDocument()
    })
  })

  describe('hook integration', () => {
    it('should use chart data from the store', () => {
      // Arrange
      const customMockStore = {
        ...mockChartStore,
        chartInfo: {
          ...mockChartInfo,
          chart: {
            ...mockChartInfo.chart,
            patientFirstName: 'CustomTestName'
          }
        }
      }
      vi.mocked(useChartStore).mockReturnValue(customMockStore)
      
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert
      expect(screen.getByDisplayValue('CustomTestName')).toBeInTheDocument()
    })

    it('should handle route parameter changes', () => {
      // Arrange - The visitId comes from chartInfo.visitID, not route params when data is available
      // So we test that the form shows the chartInfo visitID
      
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - The visitId should be populated from chart data
      // This tests the integration with our form data hook
      expect(screen.getByDisplayValue('TEST123456')).toBeInTheDocument()
    })
  })

  describe('responsive layout', () => {
    it('should render with proper container structure', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - Check for Material-UI container and layout structure
      const main = screen.getByRole('main')
      expect(main).toHaveStyle({ display: 'flex' })
      expect(main).toHaveStyle({ flexDirection: 'column' })
    })

    it('should have sticky header positioning', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - Check for sticky header styling
      const header = screen.getByRole('banner') || document.querySelector('header')
      if (header) {
        expect(header).toHaveStyle({ position: 'sticky' })
      }
    })
  })

  describe('error states', () => {
    it('should handle missing chart data gracefully', () => {
      // Arrange
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        chartInfo: null
      })
      
      // Act & Assert - Should not crash
      expect(() => renderWithProviders(<ChartPage />)).not.toThrow()
    })

    it('should render with empty form fields when no data', () => {
      // Arrange
      vi.mocked(useChartStore).mockReturnValue({
        ...mockChartStore,
        chartInfo: null
      })
      
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert - Form should still render with empty fields
      expect(screen.getByLabelText(/visit id/i)).toHaveValue('')
      expect(screen.getByLabelText(/^last$/i)).toHaveValue('')
    })
  })

  describe('accessibility', () => {
    it('should have proper ARIA labels for tabs', () => {
      // Act
      renderWithProviders(<ChartPage />)
      
      // Assert
      const tablist = screen.getByRole('tablist')
      expect(tablist).toHaveAttribute('aria-label', expect.stringContaining('chart'))
      
      const tabs = screen.getAllByRole('tab')
      tabs.forEach(tab => {
        expect(tab).toHaveAttribute('aria-selected')
      })
    })

    it('should support keyboard navigation', async () => {
      // Arrange
      renderWithProviders(<ChartPage />)
      const edTab = screen.getByRole('tab', { name: /ed/i })
      
      // Act
      edTab.focus()
      fireEvent.keyDown(edTab, { key: 'ArrowRight' })
      
      // Assert - Should be able to navigate with keyboard
      // Note: Full keyboard navigation testing might require more complex setup
      expect(document.activeElement).toBeDefined()
    })
  })
}) 