/**
 * useChartConfigInstance Hook
 * 
 * Custom hook that gets the configuration instance OID for the current chart.
 * Uses the configInstanceVersion from the chart's BOChart data directly.
 */
import useChartStore from '../../stores/chartStore';

export interface UseChartConfigInstanceReturn {
  /** Configuration instance OID from the chart's configInstanceVersion */
  configInstanceOid: number | null;
  
  /** Whether the chart data is available */
  hasChartData: boolean;
  
  /** Whether the chart data is still loading */
  isLoading: boolean;
  
  /** Facility ID from the chart data */
  facilityId: number | null;
  
  /** Visit ID from the chart data */
  visitId: string | null;
}

/**
 * Hook to get configuration instance OID for the current chart.
 * This uses the chart.configInstanceVersion directly from the BOChart data.
 */
export const useChartConfigInstance = (): UseChartConfigInstanceReturn => {
  const { chartInfo, isLoading } = useChartStore();

  const facilityId = chartInfo?.facility ?? null;
  const visitId = chartInfo?.visitID ?? null;
  
  // Use the configInstanceVersion from the chart data as the config instance OID
  const configInstanceOid = chartInfo?.chart?.configInstanceVersion ?? null;
  
  console.log('useChartConfigInstance debug:', {
    hasChartData: !!chartInfo,
    facilityId,
    visitId,
    configInstanceOid,
    configInstanceVersion: chartInfo?.chart?.configInstanceVersion,
    usingChartConfigInstance: true
  });

  return {
    configInstanceOid,
    hasChartData: !!chartInfo,
    isLoading,
    facilityId,
    visitId
  };
};
