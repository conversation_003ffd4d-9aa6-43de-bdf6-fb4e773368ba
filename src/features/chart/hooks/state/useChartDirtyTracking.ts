import { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import type { ChartFormValues } from '../data/useChartFormData';
import { chartLogger } from '../../../../utils/logger';
import { FIELD_TO_TAB_MAP } from '../../config/tabFieldMapping';

export interface ChartDirtyState {
  /** Overall form dirty state */
  isDirty: boolean;
  /** Per-tab dirty state */
  tabDirtyState: Record<number, boolean>;
  /** Whether any tab is dirty (used for save button) */
  hasAnyDirtyTab: boolean;
  /** List of dirty field names for debugging */
  dirtyFields: string[];
}

/**
 * Hook to track dirty state for the chart form, both overall and per-tab
 * 
 * This hook:
 * - Tracks which form fields have been modified
 * - Maps dirty fields to their corresponding tabs
 * - Provides per-tab dirty indicators
 * - Enables save button only when changes exist
 * 
 * @returns ChartDirtyState object with dirty tracking information
 */
export const useChartDirtyTracking = (): ChartDirtyState => {
  const formContext = useFormContext<ChartFormValues>();

  // Extract form state (hooks must be called unconditionally)
  const { formState } = formContext || { formState: { isDirty: false, dirtyFields: {} } };
  const { isDirty, dirtyFields } = formState;

  // Calculate per-tab dirty state
  const tabDirtyState = useMemo(() => {
    // Handle case when hook is called outside FormProvider
    if (!formContext) {
      return { 0: false, 1: false, 2: false };
    }

    const tabState: Record<number, boolean> = {
      0: false, // ED tab
      1: false, // Obs tab
      2: false, // Profee tab
    };

    // Check each dirty field and mark its corresponding tab as dirty
    Object.keys(dirtyFields || {}).forEach(fieldName => {
      const tabIndex = FIELD_TO_TAB_MAP[fieldName];

      if (tabIndex !== undefined && tabIndex >= 0) {
        tabState[tabIndex] = true;
      } else if (tabIndex === -1) {
        // Shared header fields affect all tabs
        tabState[0] = true;
        tabState[1] = true;
        tabState[2] = true;
      } else {
        // Log unmapped fields for debugging
        chartLogger.debug('Unmapped dirty field detected', { fieldName });
      }
    });

    return tabState;
  }, [formContext, dirtyFields]);

  // Calculate if any tab is dirty
  const hasAnyDirtyTab = useMemo(() => {
    return Object.values(tabDirtyState).some(Boolean);
  }, [tabDirtyState]);

  // Create list of dirty field names
  const dirtyFieldNames = useMemo(() => {
    return Object.keys(dirtyFields || {});
  }, [dirtyFields]);

  // Debug logging for dirty state changes
  useEffect(() => {
    if (!formContext) {
      chartLogger.warn('useChartDirtyTracking called outside FormProvider context');
      return;
    }

    if (isDirty) {
      chartLogger.debug('Chart dirty state changed', {
        isDirty,
        dirtyFields: dirtyFieldNames,
        tabDirtyState,
        hasAnyDirtyTab
      });
    }
  }, [formContext, isDirty, dirtyFieldNames, tabDirtyState, hasAnyDirtyTab]);

  // Return early if no form context, but after all hooks have been called
  if (!formContext) {
    return {
      isDirty: false,
      tabDirtyState: { 0: false, 1: false, 2: false },
      hasAnyDirtyTab: false,
      dirtyFields: [],
    };
  }

  return {
    isDirty,
    tabDirtyState,
    hasAnyDirtyTab,
    dirtyFields: dirtyFieldNames,
  };
};

/**
 * Hook to get dirty state for a specific tab
 * 
 * @param tabIndex - The tab index to check (0 = ED, 1 = Obs, 2 = Profee)
 * @returns boolean indicating if the specified tab is dirty
 */
export const useTabDirtyState = (tabIndex: number): boolean => {
  const { tabDirtyState } = useChartDirtyTracking();
  return tabDirtyState[tabIndex] || false;
};
