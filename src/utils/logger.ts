/**
 * Application Logger Utility
 * 
 * Provides environment-aware logging with proper performance considerations.
 * In production, only errors and warnings are logged to avoid performance overhead.
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface Logger {
  debug: (message: string, data?: unknown) => void;
  info: (message: string, data?: unknown) => void;
  warn: (message: string, data?: unknown) => void;
  error: (message: string, error?: Error | unknown) => void;
  perf: (operation: string, data?: unknown) => void;
}

const isDevelopment = import.meta.env.DEV;
const isDebugEnabled = isDevelopment || localStorage.getItem('debug') === 'true';

const createLogger = (context?: string): Logger => {
  const formatMessage = (level: LogLevel, message: string) => {
    const timestamp = new Date().toISOString().slice(11, 23);
    const prefix = context ? `[${context}]` : '';
    return `${timestamp} ${level.toUpperCase()} ${prefix} ${message}`;
  };

  return {
    debug: (message: string, data?: unknown) => {
      if (isDebugEnabled) {
        console.log(formatMessage('debug', message), data || '');
      }
    },

    info: (message: string, data?: unknown) => {
      if (isDevelopment) {
        console.info(formatMessage('info', message), data || '');
      }
    },

    warn: (message: string, data?: unknown) => {
      console.warn(formatMessage('warn', message), data || '');
    },

    error: (message: string, error?: Error | unknown) => {
      console.error(formatMessage('error', message), error || '');
    },

    perf: (operation: string, data?: unknown) => {
      if (isDebugEnabled) {
        console.log(formatMessage('debug', `[PERF] ${operation}`), data || '');
      }
    }
  };
};

// Default logger
export const logger = createLogger();

// Context-specific loggers
export const chartLogger = createLogger('Chart');
export const storeLogger = createLogger('Store');
export const apiLogger = createLogger('API');

export default logger; 