# Facility Data Access Enhancement

## Overview

This enhancement provides access to the complete facility data returned by the API once a facility is selected. Previously, only basic information (id and name) was available. Now you can access all facility fields to make informed decisions about chart rendering and other facility-specific logic.

## Available Data Fields

The `FacilityListItem` interface provides access to these fields:

```typescript
interface FacilityListItem {
  facilityID: string;
  longName: string;
  hasPendingConfig: boolean;
  facilityOid: number;
  oid?: number;
  isDefault: boolean;
  pendingConfigInstanceOid?: number;
  configInstanceOid?: number;
  sharedConfigInstanceOid?: number;
}
```

## Usage Methods

### 1. Using the useFacilityInfo Hook (Recommended)

The easiest way to access facility data from any component:

```typescript
import { useFacilityInfo } from '../shared/hooks/useFacilityInfo';

const MyChartComponent = () => {
  const {
    facilityInfo,
    hasPendingConfig,
    isDefault,
    facilityOid,
    configInstanceOid
  } = useFacilityInfo();

  // Make decisions based on facility configuration
  if (hasPendingConfig) {
    return <PendingConfigMessage />;
  }

  // Use facility OID for API calls
  const chartData = useChartData(facilityOid);

  // Render different chart types based on facility configuration
  if (configInstanceOid) {
    return <StandardChart data={chartData} />;
  } else {
    return <BasicChart data={chartData} />;
  }
};
```

### 2. Within APL Page Component

Access facility data directly from the APL page hooks:

```typescript
import { useFacilityData } from '../hooks/useFacilityData';

const AplChartComponent = () => {
  // Since APL page now uses hooks directly, access facility data the same way
  const facilityData = useFacilityData();
  
  // Access full facility data for the selected facility
  const facilityConfig = facilityData.selectedFacilityListItem;
  
  // Access all facilities raw data for comparison
  const allFacilities = facilityData.facilities;

  // Make chart rendering decisions
  const chartType = facilityConfig?.hasPendingConfig ? 'pending' : 'standard';
  
  return (
    <Chart 
      data={aplRecords} // You'd get this from useAplSearch hook
      type={chartType}
      facilityId={facilityConfig?.facilityOid}
    />
  );
};
```

**Note**: The APL page architecture was recently simplified from Context Provider pattern to direct hook usage. If you're building components within the APL page, use the hooks directly rather than a context.

### 3. Using AppContext Directly

Access facility data from any component in the app:

```typescript
import { useAppContext } from '../shared/contexts/AppContext';

const GlobalComponent = () => {
  const { selectedFacilityFull } = useAppContext();

  if (!selectedFacilityFull) {
    return <div>No facility selected</div>;
  }

  return (
    <div>
      <h2>{selectedFacilityFull.longName}</h2>
      <p>Facility OID: {selectedFacilityFull.facilityOid}</p>
      <p>Has Pending Config: {selectedFacilityFull.hasPendingConfig ? 'Yes' : 'No'}</p>
      <p>Is Default: {selectedFacilityFull.isDefault ? 'Yes' : 'No'}</p>
    </div>
  );
};
```

## Common Use Cases

### Chart Rendering Decisions

```typescript
const ChartSelector = () => {
  const { facilityInfo, hasPendingConfig, configInstanceOid } = useFacilityInfo();

  const getChartComponent = () => {
    if (hasPendingConfig) {
      return PendingConfigChart;
    }
    
    if (configInstanceOid) {
      return AdvancedChart;
    }
    
    return BasicChart;
  };

  const ChartComponent = getChartComponent();
  return <ChartComponent facility={facilityInfo} />;
};
```

### API Calls with Facility Context

```typescript
const useChartData = () => {
  const { facilityOid, configInstanceOid } = useFacilityInfo();

  return useQuery({
    queryKey: ['chartData', facilityOid, configInstanceOid],
    queryFn: async () => {
      const params = {
        facilityOid,
        ...(configInstanceOid && { configId: configInstanceOid })
      };
      return fetchChartData(params);
    },
    enabled: !!facilityOid
  });
};
```

### Conditional Rendering Based on Facility Type

```typescript
const FacilitySpecificFeatures = () => {
  const { facilityInfo, isDefault } = useFacilityInfo();

  return (
    <>
      {isDefault && <DefaultFacilityBadge />}
      {facilityInfo?.hasPendingConfig && <PendingConfigWarning />}
      {facilityInfo?.sharedConfigInstanceOid && <SharedConfigIndicator />}
    </>
  );
};
```

## Data Flow

1. **API Call**: `useFacilitiesQuery()` fetches all facility data
2. **Hook Processing**: `useFacilityData()` transforms and exposes raw data
3. **Context Sync**: APL page directly syncs selected facility data to `AppContext`
4. **Component Access**: Components use `useFacilityInfo()` for easy access

**Note**: The data flow was recently simplified. The APL page no longer uses a separate context provider but instead coordinates facility data synchronization directly through hooks.

## Benefits

- **Complete Data Access**: All facility fields available for decision making
- **Type Safety**: Full TypeScript support with proper interfaces
- **Performance**: Data is memoized and only updates when facility changes
- **Flexibility**: Multiple access patterns for different use cases
- **Maintainability**: Centralized facility data management

## Migration Guide

If you were previously accessing only basic facility info:

**Before:**

```typescript
const { selectedFacility } = useAppContext();
// Only had access to { id: string, name: string }
```

**After:**

```typescript
const { selectedFacility, facilityInfo } = useFacilityInfo();
// selectedFacility: basic info (unchanged)
// facilityInfo: complete facility data with all fields
```

This enhancement is backward compatible - existing code continues to work unchanged.
