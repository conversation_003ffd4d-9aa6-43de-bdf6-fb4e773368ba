# Chart Page Architecture Guide

*For developers new to React, TypeScript, Zustand, MUI, and React Hook Form*

## Overview

The Chart Page system displays and edits patient chart information. It demonstrates modern React patterns including component composition, custom hooks, and state management.

### What It Does
- Displays patient chart data (demographics, visit info, medical procedures)
- Allows editing through forms
- Manages E&M level selections
- Handles data fetching and error states
- Provides tabbed interface

## Technology Stack

### React (UI Library)
- **Purpose**: Builds user interfaces with reusable components
- **Key Concepts**: Components, props, hooks, state
- **In Our App**: All UI components are React components

### TypeScript (Type Safety)
- **Purpose**: Adds static typing to JavaScript
- **Key Concepts**: Interfaces, types, generics
- **In Our App**: All files use `.tsx` extension with strong typing

### Zustand (Global State)
- **Purpose**: Lightweight state management
- **Key Concepts**: Stores, actions, selectors
- **In Our App**: Manages chart data from API

### React Hook Form (Form State)
- **Purpose**: Manages form inputs and validation
- **Key Concepts**: Controllers, validation, form state
- **In Our App**: Handles all chart input fields

### Material-UI (UI Components)
- **Purpose**: Pre-built, styled components
- **Key Concepts**: Themes, sx props, responsive design
- **In Our App**: All buttons, inputs, layouts

## Component Architecture

### Component Hierarchy
```
ChartContainer (Data Manager)
└── ChartPage (Main Orchestrator)
    ├── ChartHeader (Patient Info & Actions)
    └── EdTabContent (Tab Content)
        └── EAndMLevelSection (Medical Procedures)
            └── EAndMLevelInline (Procedure Selection Modal)
```

### How Components Are Used
Yes, we are still using **ChartContainer**! The routing system works like this:

```typescript
// In your routes configuration
<Route path="/chart/:visitId" element={<ChartContainer />} />
```

When a user navigates to `/chart/12345`, ChartContainer loads first and handles all the data fetching, then renders ChartPage when ready.

### ChartContainer (Smart Component)
**Role**: Data fetching and error handling

```typescript
const ChartContainer: React.FC = () => {
  const { fetchChartData, isLoading, error, chartInfo } = useChartStore();
  
  if (isLoading) return <LoadingPage />;
  if (error) return <ErrorDisplay />;
  if (chartInfo) return <ChartPage />;
};
```

**Responsibilities**:
- Fetch chart data from API
- Handle loading and error states
- Only render ChartPage when data is ready
- Clean up data when unmounting

### ChartPage (Presenter Component)
**Role**: Orchestrates UI and user interactions

```typescript
const ChartPage: React.FC = () => {
  // Data transformation
  const formData = useChartFormData(chartInfo, routeVisitId);
  
  // UI state management
  const { selectedTab, handleTabChange } = useChartUIState();
  
  // Form effects
  useChartFormEffects(formData, isDirty, reset, setValue);
  
  return <FormProvider><ChartHeader /><Tabs />...</FormProvider>;
};
```

**Responsibilities**:
- Coordinate custom hooks
- Provide form context to children
- Render main layout
- Handle tab switching

## State Management

### Two-Layer State Management

#### Layer 1: Zustand Store (Global/Server State)
```typescript
const useChartStore = create<ChartState>((set) => ({
  chartInfo: null,           // Chart data from API
  isLoading: false,          // Loading state
  error: null,               // Error state
  
  fetchChartData: async (facilityId, visitId) => {
    // API call logic
  }
}));
```

**What Goes Here**:
- ✅ Data from API calls
- ✅ Loading/error states
- ✅ Data that persists across routes
- ❌ Form input values (use React Hook Form)
- ❌ UI state like tabs (use local state)

#### Layer 2: React Hook Form (Form State)
```typescript
const methods = useForm<ChartFormValues>({
  defaultValues: {
    visitId: '',
    firstName: '',
    lastName: ''
  }
});
```

**What Goes Here**:
- ✅ All form input values
- ✅ Form validation
- ✅ Dirty state tracking
- ✅ Form submission
- ❌ Global application state

## Data Flow

### Data Fetching Flow
```
1. User navigates to /chart/:visitId
2. ChartContainer gets visitId from URL
3. ChartContainer calls Zustand store
4. Store makes API call and updates state
5. ChartContainer re-renders when store updates
6. ChartPage renders when data is loaded
```

### Data Transformation Flow
```
Raw API Data → useChartFormData Hook → React Hook Form → UI Components
     ↓                                                         ↑
Zustand Store                                          Form Controllers
```

### User Interaction Flow
```
User Input → Controller → React Hook Form → Form State → UI Update
     ↑                                                       ↓
Form Reset ← useChartFormEffects ← New Data ← API Fetch
```

### Full Data Flow Diagram
```
1. URL Route (/chart/:visitId)
   ↓
2. ChartContainer (mounts, extracts visitId)
   ↓
3. Zustand Store (fetchChartData API call)
   ↓
4. ChartPage (renders when data ready)
   ↓
5. useChartFormData (transforms API data)
   ↓
6. React Hook Form (manages form state)
   ↓
7. Form Controllers (bind inputs)
   ↓
8. MUI Components (render UI)
```

## Custom Hooks

### Why Custom Hooks?
- ✅ **Testable**: Logic can be tested independently
- ✅ **Reusable**: Same logic in multiple components
- ✅ **Organized**: Clear separation of concerns
- ✅ **Maintainable**: Easier to debug

### Our Three Main Hooks
The ChartPage uses three custom hooks that work together:

1. **useChartFormData**: Transforms API data for forms
2. **useChartUIState**: Manages UI state (tabs, modals)
3. **useChartFormEffects**: Handles form side effects

This separation follows the **Single Responsibility Principle** - each hook has one clear job.

### useChartFormData (Data Transformation)
```typescript
export const useChartFormData = (
  chartInfo: BOChartInfo | null, 
  routeVisitId: string | undefined
): ChartFormValues | null => {
  return useMemo(() => {
    if (!chartInfo) return null;
    
    return {
      visitId: chartInfo.visitID || routeVisitId || '',
      firstName: chart.patientFirstName || '',
      dob: formatDate(chart.dateOfService)
    };
  }, [chartInfo, routeVisitId]);
};
```

### useChartUIState (UI State Management)
```typescript
export const useChartUIState = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  return { selectedTab, handleTabChange };
};
```

### useChartFormEffects (Side Effects)
```typescript
export const useChartFormEffects = (
  formData: ChartFormValues | null,
  isDirty: boolean,
  reset: UseFormReset<ChartFormValues>
) => {
  useEffect(() => {
    if (formData && !isDirty) {
      reset(formData, { keepDirty: false });
    }
  }, [formData, isDirty, reset]);
};
```

## Form Management

### React Hook Form Pattern

React Hook Form gives us **uncontrolled components** with **controlled behavior**. This means:
- Form inputs are not re-rendered on every keystroke (performance)
- Form state is managed efficiently
- Validation happens at the right time
- Integration with MUI components is seamless

#### Form Provider (Context)
```typescript
// ChartPage.tsx - Form setup
const methods = useForm<ChartFormValues>({ 
  defaultValues: {
    visitId: '',
    firstName: '',
    lastName: '',
    // ... all form fields
  }
});

return (
  <FormProvider {...methods}>
    <ChartHeader />      {/* Can use form context */}
    <EdTabContent />     {/* Can use form context */}
  </FormProvider>
);
```

#### Form Controllers (Input Binding)
```typescript
// ChartHeader.tsx - Using form context
const { control } = useFormContext();

<Controller
  name="firstName"              // Must match defaultValues key
  control={control}             // Form control object
  render={({ field }) => (     // Render prop pattern
    <TextField 
      label="First Name" 
      {...field}                // Spreads: value, onChange, onBlur, name
      sx={textFieldSx}          // MUI styling
    />
  )}
/>
```

#### Form State Management
```typescript
// ChartPage.tsx - Form state access
const { 
  reset,                    // Reset form to new values
  formState: { isDirty },   // Has user made changes?
  setValue                  // Programmatically set field values
} = methods;

// Example: Reset form when new data arrives
useEffect(() => {
  if (newData && !isDirty) {
    reset(newData, { keepDirty: false });
  }
}, [newData, isDirty, reset]);
```

## UI Components

### Material-UI Integration

#### Theme System
```typescript
<TextField 
  sx={{ 
    backgroundColor: 'background.paper',
    borderRadius: 1
  }} 
/>
```

#### Responsive Design
```typescript
<Grid item xs={12} sm={6} md={3}>
  <TextField fullWidth />
</Grid>
```

## Best Practices

### Component Design
```typescript
// ✅ Good: Single responsibility
const ChartHeader = () => {
  // Only handles patient info and actions
};

// ❌ Bad: Multiple responsibilities
const ChartEverything = () => {
  // Data fetching, form state, UI state, rendering...
};
```

### State Management
```typescript
// ✅ Good: Right tool for right job
const chartInfo = useChartStore(state => state.chartInfo);  // Global
const { control } = useFormContext();                      // Form
const [selectedTab, setSelectedTab] = useState(0);         // Local UI

// ❌ Bad: Wrong tool
const [chartInfo, setChartInfo] = useState(null);          // Should be global
```

### TypeScript Usage
```typescript
// ✅ Good: Explicit interfaces
interface ChartHeaderProps {
  textFieldSx: SxProps<Theme>;
}

// ❌ Bad: Any types
const data: any = {...};
```

## Common Patterns

### Container/Presenter Pattern
```typescript
// Container: Data and logic
const ChartContainer = () => {
  const { data, loading, error } = useChartStore();
  if (loading) return <Loading />;
  return <ChartPage data={data} />;
};

// Presenter: UI rendering
const ChartPage = ({ data }) => {
  return <UI components />;
};
```

### Compound Components Pattern
```typescript
<FormProvider methods={methods}>
  <ChartHeader />
  <EdTabContent />
</FormProvider>
```

## Debugging Tips

### Common Issues

#### "Cannot read property of undefined"
```typescript
// ❌ Problem
const name = chartInfo.chart.patientFirstName;

// ✅ Solution: Optional chaining
const name = chartInfo?.chart?.patientFirstName || '';
```

#### "Form not updating with new data"
```typescript
// ✅ Solution: Reset form with new data
useEffect(() => {
  if (data && !isDirty) {
    reset(transformedData);
  }
}, [data, isDirty, reset]);
```

## Typical Development Workflow

### Adding a New Form Field
```typescript
// 1. Add to type definition (useChartFormData.ts)
export type ChartFormValues = {
  // ... existing fields
  newField: string;
};

// 2. Add to defaultValues (ChartPage.tsx)
const methods = useForm<ChartFormValues>({
  defaultValues: {
    // ... existing fields
    newField: '',
  }
});

// 3. Add to data transformation (useChartFormData.ts)
return {
  // ... existing transformations
  newField: chartInfo?.someApiProperty || '',
};

// 4. Add UI component (ChartHeader.tsx or EdTabContent.tsx)
<Controller
  name="newField"
  control={control}
  render={({ field }) => (
    <TextField label="New Field" {...field} sx={textFieldSx} />
  )}
/>
```

### Adding a New Tab
```typescript
// 1. Add tab state (useChartUIState.ts)
const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
  setSelectedTab(newValue);
};

// 2. Add tab UI (ChartPage.tsx)
<Tab label="New Tab" />

// 3. Add tab content (ChartPage.tsx)
{selectedTab === 3 && (
  <NewTabContent />
)}

// 4. Create content component (NewTabContent.tsx)
const NewTabContent = () => {
  const { control } = useFormContext();
  return <Paper>/* Tab content */</Paper>;
};
```

## File Structure Reference

```
src/features/chart/
├── components/
│   ├── ChartContainer.tsx       ← Entry point (handles data fetching)
│   ├── ChartPage.tsx           ← Main UI orchestrator
│   ├── ChartHeader.tsx         ← Patient info form fields
│   ├── EdTabContent.tsx        ← ED tab content
│   └── EAndMLevelSection.tsx   ← Medical procedure selections
├── hooks/
│   ├── useChartFormData.ts     ← Data transformation
│   ├── useChartUIState.ts      ← UI state management
│   └── useChartFormEffects.ts  ← Form side effects
├── stores/
│   └── chartStore.ts           ← Zustand store (API data)
└── contexts/
    └── (none - using React Hook Form context)
```

## Next Steps

### For New Developers
1. **Start here**: Read ChartContainer.tsx to understand entry point
2. **Then**: Follow data flow from ChartContainer → ChartPage → custom hooks
3. **Practice**: Add a simple form field using the workflow above
4. **Learn**: How React Hook Form Controller pattern works
5. **Explore**: How Zustand store manages API data

### For Feature Development
1. **Identify**: Which layer needs changes (API, Store, Transform, UI)
2. **Plan**: How data flows through the system
3. **Test**: Use browser dev tools to inspect form state
4. **Follow**: Existing patterns for consistency

### Debugging Tools
- **React DevTools**: Inspect component props and state
- **Zustand DevTools**: Monitor store state changes
- **React Hook Form DevTools**: See form state in real-time
- **Browser Console**: Check our custom logger output

### Resources
- [React Documentation](https://react.dev)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [React Hook Form Documentation](https://react-hook-form.com/)
- [Material-UI Documentation](https://mui.com/)

---

*This guide documents the current Chart Page architecture. As you work with the code, refer back to these patterns and workflows to maintain consistency and quality.* 