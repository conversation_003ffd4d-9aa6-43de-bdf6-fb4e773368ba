# APL Page Architecture Documentation

## Overview

The APL (Authorized Patient List) page follows a modern React architecture pattern using hooks, context, and component composition. This document outlines the complete object hierarchy and relationships within the APL feature.

## High-Level Architecture Diagram

```text
AplPage (Root Container)
├── AplPageProvider (Context Provider)
│   └── AplPageContent (Main UI Component)
│       ├── FacilityStatus
│       ├── VisitIdField
│       ├── SmartDatePicker (From Date)
│       ├── SmartDatePicker (To Date)
│       ├── FormControlLabel (Checkbox)
│       ├── Button (Search)
│       └── SearchResults
│           └── DataRequesterGrid
│               ├── CustomGridToolbar
│               └── GridActionToolbar
```

## Component Hierarchy & Relationships

### 1. Top-Level Components

#### `AplPage` (Container)

- **Purpose**: Root component that provides context wrapping
- **Pattern**: Provider pattern wrapper
- **Children**: `AplPageContent`
- **Dependencies**: `AplPageProvider`

```tsx
AplPage
├── Wraps content with AplPageProvider
└── Renders AplPageContent
```

#### `AplPageContent` (Main UI)

- **Purpose**: Main UI layout and form controls
- **Dependencies**: Material-UI components, custom hooks
- **State Management**: Consumes `AplPageContext`
- **Event Handlers**: Uses `useAplPageHandlers`

### 2. Context & State Management

#### `AplPageContext` (State Orchestrator)

- **Purpose**: Centralized state management for entire APL feature
- **Pattern**: Context Provider with custom hook composition
- **Combines Multiple Hooks**:
  - `useFacilityData`
  - `useFormState`
  - `useAplSearch`
- **App Integration**: Syncs with `AppContext`

```typescript
AplPageContext
├── Form State (visitId, mrn, dates, checkbox)
├── Facility Data (selection, list, loading states)
├── Search Results (records, loading, errors)
└── Event Handlers (fetch, refresh, filter)
```

### 3. Custom Hooks Architecture

#### `useFacilityData`

- **Purpose**: Manages facility selection and API data
- **Features**:
  - URL parameter synchronization (`/apl/facility/:id`)
  - Default facility selection logic
  - Facility list transformation
- **Dependencies**: React Router, facility API service

#### `useFormState`

- **Purpose**: Form field state management
- **Features**:
  - Conditional URL synchronization (APL routes only)
  - Date range management with validation
  - Form field persistence
- **Integration**: Uses `useUrlDateParams` for date URL sync

#### `useAplSearch`

- **Purpose**: APL data fetching and search logic
- **Features**:
  - React Query integration for caching
  - Background refresh capabilities
  - Search filter validation
  - Error handling with user-friendly messages
- **Dependencies**: React Query, APL API service

#### `useAplPageHandlers`

- **Purpose**: Event handler functions
- **Features**:
  - Facility change navigation
  - Search execution
  - Memoized callbacks for performance
- **Dependencies**: React Router navigation

#### `useUrlDateParams` (Supporting Hook)

- **Purpose**: URL synchronization for date parameters
- **Features**:
  - Bidirectional URL/state sync
  - Date parsing and formatting
  - URL parameter management

### 4. Child Components

#### `FacilityStatus`

- **Purpose**: Facility selection dropdown with status indicators
- **Props**: Loading states, error states, facility list, selection handlers
- **Features**: Loading spinners, error alerts, facility dropdown

#### `VisitIdField`

- **Purpose**: Visit ID input field
- **Props**: Value, onChange handler
- **Features**: Text input with validation

#### `SearchResults` (Container)

- **Purpose**: Search results container with state management
- **Features**:
  - Loading state display
  - Error message handling
  - Result count display
  - Grid orchestration
- **Children**: `DataRequesterGrid`

#### `DataRequesterGrid` (Advanced Grid)

- **Purpose**: High-performance data grid for APL records
- **Features**:
  - MUI DataGrid Premium integration
  - Column management and persistence
  - Filtering and sorting
  - Virtual scrolling for large datasets
  - Row navigation on double-click
- **Children**: `CustomGridToolbar`, `GridActionToolbar`

## Data Flow Architecture

### 1. Data Flow Patterns

```text
User Action → Event Handler → Context State Update → Component Re-render
     ↓
URL Update (if applicable) → Route Change → State Sync
     ↓
API Call → Loading State → Success/Error → UI Update
```

### 2. State Synchronization Flow

```text
Component State ←→ Context State ←→ URL Parameters ←→ App Context
                        ↓
                   API Service ←→ Server
```

### 3. Search Flow

```text
1. User Input (Facility/Dates/VisitID) → Form State
2. Facility Change → URL Navigation → Auto-fetch
3. Manual Search → Button Click → API Call
4. Results → Context State → Grid Display
```

## API Integration

### 1. Data Types & Interfaces

#### `AplRecord`

```typescript
interface AplRecord {
  id: string;
  cfExportPhysicianStatus?: string;
  cfExportFacilityChartVersion?: number;
  // ... 30+ additional fields
  mrn?: string;
  visitId?: string;
  dos?: string; // Date of Service
}
```

#### `AplSearchFilters`

```typescript
interface AplSearchFilters {
  facilityId: string;
  fromDate: Date | null;
  toDate: Date | null;
  visitId?: string;
  mrn?: string;
}
```

### 2. API Service Integration

```text
useAplSearch Hook
├── React Query (useQuery)
├── aplRecordsApi Service
├── Authentication Integration
└── Error Handling
```

## State Management Patterns

### 1. Context Provider Pattern

- **Central State**: All APL-related state in one context
- **Hook Composition**: Multiple focused hooks combined
- **Provider Wrapping**: Clean separation of concerns

### 2. Custom Hook Composition

```typescript
// Context combines focused hooks
const facilityData = useFacilityData();     // Facility management
const formState = useFormState();           // Form fields
const aplSearch = useAplSearch();           // Search & results

// Unified interface returned to components
return { ...facilityData, ...formState, ...aplSearch };
```

### 3. URL Synchronization Strategy

- **Conditional Sync**: Only sync when on APL routes
- **Bidirectional**: URL ↔ State synchronization
- **Navigation Integration**: React Router for facility routing

## Performance Optimizations

### 1. React Performance Patterns

- **useCallback**: Memoized event handlers
- **useMemo**: Expensive computations cached
- **React.memo**: Component re-render prevention
- **Background Fetching**: Non-blocking data refresh

### 2. Grid Performance

- **Virtual Scrolling**: Handle large datasets
- **Column State Persistence**: Layout saved to localStorage
- **Filtering/Sorting**: Client-side performance optimization

### 3. API Performance

- **React Query Caching**: Automatic request deduplication
- **Background Refresh**: Keep data fresh without UI blocking
- **Error Boundaries**: Graceful error handling

## Error Handling Strategy

### 1. Error Boundaries

- Component-level error containment
- User-friendly error messages
- Fallback UI components

### 2. API Error Handling

- Network error detection
- Authentication error handling
- Validation error display
- Retry mechanisms

### 3. Form Validation

- Date range validation
- Required field validation
- Real-time feedback

## Security Considerations

### 1. Authentication Integration

- Token-based authentication
- Automatic token refresh
- Secure API communication

### 2. Authorization

- Facility-based access control
- Role-based permissions
- Secure route protection

## Future Enhancement Opportunities

### 1. Performance

- Implement React Query mutations for data updates
- Add client-side caching for facility data
- Optimize grid rendering for mobile devices

### 2. User Experience

- Add keyboard shortcuts for common actions
- Implement advanced search filters
- Add export functionality

### 3. Architecture

- Consider migrating to React Query for all API calls
- Implement proper error boundaries at feature level
- Add comprehensive loading states

## Development Guidelines

### 1. Adding New Features

1. Create focused custom hooks for new functionality
2. Integrate hooks into AplPageContext
3. Add TypeScript interfaces for new data types
4. Update this documentation

### 2. Component Development

- Follow single responsibility principle
- Use TypeScript for all props and interfaces
- Implement proper error handling
- Add comprehensive JSDoc comments

### 3. Testing Strategy

- Unit tests for custom hooks
- Integration tests for context providers
- E2E tests for critical user workflows
- API service mocking for reliable tests

This architecture provides a scalable, maintainable foundation for the APL feature while following React best practices and modern development patterns.
