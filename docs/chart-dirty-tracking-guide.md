# Chart Dirty Tracking Implementation Guide

## Overview

The chart page now includes comprehensive dirty tracking functionality that:
- Tracks changes to form fields on a per-tab basis
- Shows visual indicators on tab headers when they contain unsaved changes
- Enables/disables the save button based on whether any changes exist
- Provides granular dirty state information for each tab

## Architecture

### Components

1. **Field-to-Tab Mapping Configuration** (`src/features/chart/config/tabFieldMapping.ts`)
   - Single source of truth for mapping form fields to tab indexes
   - Optimized flat structure for O(1) lookups
   - Helper functions for field-to-tab operations

2. **Dirty Tracking Hook** (`src/features/chart/hooks/ui/useChartUIState.ts`)
   - `useTabDirtyTracking()` - Core hook for tracking dirty state per tab
   - `useChartUIStateWithDirtyTracking()` - Enhanced UI state hook with dirty tracking

3. **Visual Indicators**
   - Tab headers show red dots (●) when they contain unsaved changes
   - Save button is only enabled when changes exist

## Usage

### Basic Usage

```typescript
import { useChartUIStateWithDirtyTracking } from '../../hooks/ui/useChartUIState';

function ChartComponent() {
  const {
    selectedTab,
    handleTabChange,
    isEdTabDirty,
    isObsTabDirty,
    isProfeeTabDirty,
    isAnyTabDirty
  } = useChartUIStateWithDirtyTracking();

  return (
    <Tabs value={selectedTab} onChange={handleTabChange}>
      <Tab label={`ED ${isEdTabDirty ? '●' : ''}`} />
      <Tab label={`Obs ${isObsTabDirty ? '●' : ''}`} />
      <Tab label={`Profee ${isProfeeTabDirty ? '●' : ''}`} />
    </Tabs>
  );
}
```

### Field Mapping

Fields are mapped to tab indexes as follows:

- **ED Tab (index 0)**: `treatmentArea`, `edChartStatus`, `note`, `levelCheckboxStates`, `mod25`, `mod59`
- **Obs Tab (index 1)**: `obsStart`, `obsEnd`, `isObs`
- **Profee Tab (index 2)**: `traumaActivation`, `criticalCareMins`, `specialNoCharge`
- **Shared Fields (index -1)**: `visitId`, `firstName`, `lastName`, `provider`, etc. (header fields that affect all tabs)

### Adding New Fields

To add a new field to dirty tracking:

1. Add the field to the `FIELD_TO_TAB_MAP` in `tabFieldMapping.ts`
2. The dirty tracking will automatically include it

```typescript
// In tabFieldMapping.ts
export const FIELD_TO_TAB_MAP: Record<string, number> = {
  // ... existing fields
  'newEdField': 0,        // Add to ED tab
  'newObsField': 1,       // Add to Obs tab
  'newProfeeField': 2,    // Add to Profee tab
  'newSharedField': -1,   // Add to shared fields
};
```

## Implementation Details

### Dirty State Detection

The system uses React Hook Form's `dirtyFields` to detect changes:
- Compares current form values with initial values
- Tracks nested objects (like `levelCheckboxStates`) properly
- Updates in real-time as user makes changes

### Save Button Logic

```typescript
<Button 
  disabled={!isAnyTabDirty}
  onClick={handleSave}
>
  Save
</Button>
```

The save button is:
- **Enabled** when any tab has unsaved changes
- **Disabled** when no changes exist
- **Visually dimmed** when disabled

### Visual Indicators

Tab headers show a red dot (●) when dirty:
```typescript
<Tab 
  label={
    <span>
      ED
      {isEdTabDirty && (
        <span style={{ color: '#f44336', marginLeft: '4px' }}>●</span>
      )}
    </span>
  } 
/>
```

## Testing

Tests are included for:
- Field mapping configuration
- Dirty tracking hook functionality
- Tab-to-field relationships

Run tests with:
```bash
npm test tabFieldMapping
```

## Future Enhancements

Potential improvements:
1. **Save Confirmation**: Warn users before navigating away with unsaved changes
2. **Auto-save**: Implement periodic auto-save functionality
3. **Field-level Indicators**: Show which specific fields are dirty
4. **Undo/Redo**: Track change history for undo functionality
5. **Save Status**: Show saving/saved status feedback
