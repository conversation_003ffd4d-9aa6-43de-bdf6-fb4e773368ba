{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm run lint)", "Bash(npx eslint:*)", "Bash(rm:*)", "WebFetch(domain:mui.com)", "Bash(rg:*)", "Bash(ls:*)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx markdownlint:*)", "Bash(npm install:*)", "Bash(npx msw init:*)", "Bash(node:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(claude config path)", "mcp__consult7__consultation", "Bash(npm audit:*)", "mcp__ide__getDiagnostics"], "deny": []}}