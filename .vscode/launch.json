{"version": "0.2.0", "configurations": [{"name": "Vite: Run dev server", "type": "node-terminal", "request": "launch", "command": "npm run dev", "cwd": "${workspaceFolder}"}, {"name": "Vite: Launch Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug", "skipFiles": ["<node_internals>/**", "node_modules/**"]}], "compounds": [{"name": "Vite: Dev + Chrome with Profile", "configurations": ["Vite: Run dev server", "Vite: Launch Chrome"], "stopAll": true}]}