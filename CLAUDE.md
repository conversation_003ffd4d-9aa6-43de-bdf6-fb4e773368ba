# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `npm run dev` - Start development server (port 5173, checks for existing servers)
- `npm run build` - TypeScript compilation + production build
- `npm run lint` - ESLint with unused disable directives check
- `npm run test` - Run Vitest tests in watch mode
- `npm run test:run` - Run tests once
- `npm run test:ui` - Run tests with Vitest UI
- `npm run test:coverage` - Generate test coverage report

### Development Server Rules
CRITICAL: Always check if dev server is running before starting new one. Multiple servers cause port conflicts and CORS issues. Check with `netstat -ano | findstr :5173` (Windows) or `lsof -i :5173` (Mac/Linux).

## Project Architecture

### High-Level Structure
This is a React + TypeScript + Vite application for healthcare facility management with two main domains:
- **APL (Authorized Patient List)**: Facility management and patient record search
- **Chart**: Patient chart viewing and medical data display

### Key Architectural Patterns

#### Feature-Based Organization
```
src/features/
├── apl/        # APL facility management
├── auth/       # Authentication system
├── chart/      # Patient chart functionality
└── shared/     # Shared components and utilities
```

#### State Management Strategy
- **React Context**: APL page uses complex context pattern with hook composition
- **Zustand**: Chart feature uses Zustand store for simpler state management
- **React Query**: API data fetching and caching (via @tanstack/react-query)

#### Context Architecture (APL Feature)
The APL feature demonstrates sophisticated React patterns:
- `AplPageContext` orchestrates multiple custom hooks
- Combines form state, facility data, and search functionality
- Auto-fetches data on facility changes with careful effect management
- Uses memoization to prevent unnecessary re-renders

#### Component Architecture
- Lazy loading for code splitting (React.lazy + Suspense)
- Feature-scoped components with clear boundaries
- Shared components for common UI patterns
- Error boundaries for graceful error handling

### Authentication Flow
- Route-based authentication with `useAuthGuard`
- Protected vs public route separation
- JWT token management with automatic refresh
- Redirect handling for unauthenticated users

### API Integration
- Centralized axios instance with interceptors
- Service layer pattern for API calls
- Proxy configuration for development (target: `a002micdev01.cloud.mdrxdev.com:6768`)
- Error handling with proper logging

### Testing Strategy
- Vitest with happy-dom environment
- React Testing Library for component tests
- Coverage reporting with v8 provider
- Test files co-located with source code

### Logging System
Context-aware logging with environment-specific behavior:
- Development: Full logging enabled
- Production: Errors and warnings only
- Specialized loggers: `chartLogger`, `storeLogger`, `apiLogger`
- Performance logging for debugging

### Key Dependencies
- **UI**: Material-UI (@mui/material) with premium data grid
- **Forms**: React Hook Form for form management
- **Routing**: React Router DOM with lazy loading
- **Date Handling**: date-fns with MUI date pickers
- **State**: Zustand for simple state, React Context for complex orchestration

### Development Guidelines
- TypeScript strict mode enabled
- ESLint + Prettier for code quality
- Feature-scoped organization
- Custom hooks for business logic separation
- Comprehensive error boundaries